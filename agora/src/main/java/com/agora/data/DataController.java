package com.agora.data;

import com.agora.core.Manager;
import com.agora.dao.*;
import com.agora.pojo.*;
import com.agora.support.CityEntry;
import com.agora.util.ParamUtils;
import com.github.slugify.Slugify;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import opennlp.tools.doccat.DocumentCategorizerME;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;

/**
 *
 * <AUTHOR>
 */
public class DataController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataController.class.getName());

    public static Route data_cities = (Request request, Response response) -> {

        String name = request.queryParams("name");

        String stringToRemove = StringUtils.substringBetween(name, "(", ")");
        if (StringUtils.isNotBlank(stringToRemove)) {
            name = StringUtils.remove(name, "(" + stringToRemove + ")");
        }
        name = StringUtils.remove(name, "(");
        name = StringUtils.remove(name, ")");
        name = StringUtils.remove(name, "^");
        name = StringUtils.remove(name, "$");

        List<City> cities = null;
        if (StringUtils.isNotBlank(name)) {
            if (StringUtils.isNumeric(name)) {
                cities = CityDao.loadCityListBy(null, name, 0, 20);
            } else {
                cities = CityDao.loadCityListBy(name, null, 0, 20);
            }
        }

        //description
        return cities;

//        String[][] names;
//        if ((cities != null) && (cities.size() > 0)) {
//            names = new String[cities.size()][3];
//            for (int i = 0; i < cities.size(); i++) {
//                names[i][0] = cities.get(i).getCity();
//                names[i][1] = cities.get(i).getPostalCode() != null ? cities.get(i).getPostalCode() : "";
//                names[i][2] = cities.get(i).getProvinceCode() != null ? cities.get(i).getProvinceCode() : "";
//            }
//        } else {
//            names = new String[0][0];
//        }
//
//        return names;
    };

    public static Route data_tag_page = (Request request, Response response) -> {

        String name = request.queryParams("name[term]");

        List<String> tags = null;
        if (StringUtils.isNotBlank(name)) {
            tags = PageDao.loadPageTagListBy(name, 0, 20);
        }

        // PRIMA METTO QUELLI CHE INIZIANO CON IL 'name', poi tutto il resto
        List<String> finalTags = new ArrayList<>();
        List<String> tmpTags = new ArrayList<>();
        if (tags != null) {
            // PRIMA DI TUTTO QUELLO RICERCATO SE C'E'
            for (String tag : tags) {
                if (StringUtils.equalsIgnoreCase(tag, name)) {
                    tmpTags.add(tag);
                }
            }
            // sort
            Collections.sort(tmpTags);
            finalTags.addAll(tmpTags);
            finalTags = new ArrayList<>();
            // POI QUELLI CHE INIZIANO CON IL 'name'
            for (String tag : tags) {
                if (StringUtils.startsWith(tag, name)) {
                    if (!tmpTags.contains(tag)) {
                        tmpTags.add(tag);
                    }
                }
            }
            // sort
            Collections.sort(tmpTags);
            finalTags.addAll(tmpTags);
            // POI TUTTI GLI ALTRI
            tmpTags = new ArrayList<>();
            for (String tag : tags) {
                if (!StringUtils.startsWith(tag, name)) {
                    if (!tmpTags.contains(tag)) {
                        tmpTags.add(tag);
                    }
                }
            }
            Collections.sort(tmpTags);
            finalTags.addAll(tmpTags);
        }

        return finalTags;
    };

    public static Route data_tag_event = (Request request, Response response) -> {

        String name = request.queryParams("name[term]");

        List<String> tags = null;
        if (StringUtils.isNotBlank(name)) {
            tags = EventDao.loadEventTagListBy(name, 0, 20);
        }

        // PRIMA METTO QUELLI CHE INIZIANO CON IL 'name', poi tutto il resto
        List<String> finalTags = new ArrayList<>();
        List<String> tmpTags = new ArrayList<>();
        if (tags != null) {
            // PRIMA DI TUTTO QUELLO RICERCATO SE C'E'
            for (String tag : tags) {
                if (StringUtils.equalsIgnoreCase(tag, name)) {
                    tmpTags.add(tag);
                }
            }
            // sort
            Collections.sort(tmpTags);
            finalTags.addAll(tmpTags);
            finalTags = new ArrayList<>();
            // POI QUELLI CHE INIZIANO CON IL 'name'
            for (String tag : tags) {
                if (StringUtils.startsWith(tag, name)) {
                    if (!tmpTags.contains(tag)) {
                        tmpTags.add(tag);
                    }
                }
            }
            // sort
            Collections.sort(tmpTags);
            finalTags.addAll(tmpTags);
            // POI TUTTI GLI ALTRI
            tmpTags = new ArrayList<>();
            for (String tag : tags) {
                if (!StringUtils.startsWith(tag, name)) {
                    if (!tmpTags.contains(tag)) {
                        tmpTags.add(tag);
                    }
                }
            }
            Collections.sort(tmpTags);
            finalTags.addAll(tmpTags);
        }

        return finalTags;
    };

    public static Route data_user = (Request request, Response response) -> {

        String name = request.queryParams("name[term]");

        List<User> users = null;
        if (StringUtils.isNotBlank(name)) {
            users = UserDao.loadUserListBy(name, 0, 20);
        }

        String[][] names;
        if (users != null && !users.isEmpty()) {
            names = new String[users.size()][2];

            for (int i = 0; i < users.size(); i++) {
                names[i][0] = users.get(i).getId().toString();
                names[i][1] = StringUtils.defaultIfBlank(users.get(i).getName(), "") + " (" + StringUtils.defaultIfBlank(users.get(i).getEmail(), "") + ")";
            }
        } else {
            return null;
        }

        return names;
    };

    public static Route data_cities_ac = (Request request, Response response) -> {

        String name = request.queryParams("name[term]");

        String stringToRemove = StringUtils.substringBetween(name, "(", ")");
        if (StringUtils.isNotBlank(stringToRemove)) {
            name = StringUtils.remove(name, "(" + stringToRemove + ")");
        }
        name = StringUtils.remove(name, "(");
        name = StringUtils.remove(name, ")");
        name = StringUtils.remove(name, "^");
        name = StringUtils.remove(name, "$");

        List<CityEntry> cities = null;
        if (StringUtils.isNotBlank(name)) {
            cities = CityDao.loadCityEntryByPattern(name);
        }

        String[][] names;
        if ((cities != null) && (!cities.isEmpty())) {
            names = new String[cities.size()][1];
            for (int i = 0; i < cities.size(); i++) {
                names[i][0] = cities.get(i).getCity();
            }
        } else {
            names = new String[0][0];
        }

        return names;
    };

    public static Route data_pages = (Request request, Response response) -> {
        String name = request.queryMap("name").value();
        ObjectId ownerId = ParamUtils.toObjectId(request.queryParams("ownerId"));
        String pageStr = request.queryMap("page").value();

        // pagination
        int pagination = 10;
        int page = 0;
        if (pageStr != null) {
            page = NumberUtils.toInt(pageStr) - 1;
        }

        int skip = page * (pagination);

        List<Page> pages = PageDao.loadPageListByName(name, ownerId, true, skip, pagination);
        long countAll = PageDao.loadPageCountBy(name, ownerId, true);

        String[][] names;
        if ((pages != null) && (pages.size() > 0)) {
            names = new String[pages.size()][6];

            for (int i = 0; i < pages.size(); i++) {
                names[i][0] = pages.get(i).getId().toString();
                names[i][1] = pages.get(i).getName();
                names[i][2] = pages.get(i).getProfileImageId() != null ? pages.get(i).getProfileImageId().toString() : null;
                names[i][3] = pages.get(i).getIdentifier();
                names[i][4] = pages.get(i).getShortDescription();
                names[i][5] = Long.toString(countAll);
            }
        } else {
            return null;
        }

        return names;
    };

    public static Route data_events = (Request request, Response response) -> {
        String name = request.queryMap("name").value();
        String pageStr = request.queryMap("page").value();

        // pagination
        int pagination = 10;
        int page = 0;
        if (pageStr != null) {
            page = NumberUtils.toInt(pageStr) - 1;
        }

        int skip = page * (pagination);

        List<Event> events = EventDao.loadEventListBy(name, skip, pagination);
        long countAll = EventDao.loadEventCountBy(name);

        String[][] names;
        if ((events != null) && (events.size() > 0)) {
            names = new String[events.size()][7];

            for (int i = 0; i < events.size(); i++) {
                names[i][0] = events.get(i).getId().toString();
                names[i][1] = events.get(i).getName();
                names[i][2] = events.get(i).getCoverImageId() != null ? events.get(i).getCoverImageId().toString() : null;
                names[i][3] = events.get(i).getIdentifier();
                names[i][4] = Long.toString(countAll);
                names[i][5] = events.get(i).getStartDate() != null ? events.get(i).getStartDate().getTime() + "" : null;
                names[i][6] = events.get(i).getEndDate() != null ? events.get(i).getEndDate().getTime() + "" : null;
            }
        } else {
            return null;
        }

        return names;
    };

    public static Route data_events_container = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        String name = request.queryMap("name").value();
        String pageStr = request.queryMap("page").value();

        // pagination
        int pagination = 10;
        int page = 0;
        if (pageStr != null) {
            page = NumberUtils.toInt(pageStr) - 1;
        }

        int skip = page * (pagination);

        List<Event> events = EventDao.loadEventContainerListBy(name, user.getId(), skip, pagination);
        long countAll = EventDao.loadEventContainerCountBy(name, user.getId());

        String[][] names;
        if ((events != null) && (!events.isEmpty())) {
            names = new String[events.size()][5];

            for (int i = 0; i < events.size(); i++) {
                names[i][0] = events.get(i).getId().toString();
                names[i][1] = events.get(i).getName();
                names[i][2] = events.get(i).getCoverImageId() != null ? events.get(i).getCoverImageId().toString() : null;
                names[i][3] = events.get(i).getIdentifier();
                names[i][4] = Long.toString(countAll);
            }
        } else {
            return null;
        }

        return names;
    };

    public static Route data_search = (Request request, Response response) -> {
        String name = request.queryMap("q").value();
        String pageStr = request.queryMap("page").value();

        // pagination
        int pagination = 10;
        int page = 0;
        if (pageStr != null) {
            page = NumberUtils.toInt(pageStr) - 1;
        }

        if (page == 0) {
            pagination = 9;
        }
        int skip = page * (pagination);

        List<Page> pages = PageDao.loadPageListBy(name, skip, pagination);
        long countAll = PageDao.loadPageCountBy(name);

        String[][] names;
        if ((pages != null) && (pages.size() > 0)) {
            if (page == 0) {
                names = new String[pages.size() + 1][6];
                names[0][1] = name;
                names[0][5] = Long.toString(countAll);
            } else {
                names = new String[pages.size()][6];
            }

            for (int i = 0; i < pages.size(); i++) {
                names[page == 0 ? i + 1 : i][0] = pages.get(i).getId().toString();
                names[page == 0 ? i + 1 : i][1] = pages.get(i).getName();
                names[page == 0 ? i + 1 : i][2] = pages.get(i).getProfileImageId() != null ? pages.get(i).getProfileImageId().toString() : null;
                names[page == 0 ? i + 1 : i][3] = pages.get(i).getIdentifier();
                names[page == 0 ? i + 1 : i][4] = pages.get(i).getShortDescription();
                names[page == 0 ? i + 1 : i][5] = Long.toString(countAll);
            }
        } else {
            names = new String[1][6];
            names[0][1] = name;
            names[0][5] = Long.toString(0);
        }

        return names;
    };

    public static Route data_customers = (Request request, Response response) -> {
        String name = request.queryParams("name");

        List<Customer> customers = CustomerDao.loadCustomerListByEmailPartial(name);
        // lastname, id, code, name

        String[][] names;
        if ((customers != null) && (customers.size() > 0)) {
            names = new String[customers.size()][5];
            for (int i = 0; i < customers.size(); i++) {
                names[i][0] = customers.get(i).getId().toString();
                names[i][1] = customers.get(i).getUserId().toString();
                names[i][2] = customers.get(i).getEmail();
                names[i][3] = customers.get(i).getLastname();
                names[i][4] = customers.get(i).getName();
            }
        } else {
            names = new String[0][0];
        }

        return names;
    };

    public static Route data_cities_with_province = (Request request, Response response) -> {

        String name = request.queryParams("name");

        String stringToRemove = StringUtils.substringBetween(name, "(", ")");
        if (StringUtils.isNotBlank(stringToRemove)) {
            name = StringUtils.remove(name, "(" + stringToRemove + ")");
        }
        name = StringUtils.remove(name, "(");
        name = StringUtils.remove(name, ")");
        name = StringUtils.remove(name, "^");
        name = StringUtils.remove(name, "$");

        List<City> cities = new ArrayList<>();
        if (StringUtils.isNotBlank(name)) {
            if (StringUtils.isNumeric(name)) {
                cities = CityDao.loadCityListBy(null, name, 0, 20);
            } else {
                List<Province> provinceList = ProvinceDao.loadProvinceListBy(name, 0, 1);
                if (!provinceList.isEmpty()) {
                    for (Province province : provinceList) {
                        City city = new City();
                        city.setCity(province.getDescription() + " e provincia");
                        city.setProvinceCode(province.getCode());
                        city.setPostalCode("ZZZZZ");
                        city.setRegion("ZZZZZ");
                        Slugify slg = new Slugify();
                        city.setIdentifier(slg.slugify(city.getCity()));
                        cities.add(city);
                    }
                }
                cities.addAll(CityDao.loadCityListBy(name, null, 0, 20));
            }
        }

        //description
        return cities;

//        String[][] names;
//        if ((cities != null) && (cities.size() > 0)) {
//            names = new String[cities.size()][3];
//            for (int i = 0; i < cities.size(); i++) {
//                names[i][0] = cities.get(i).getCity();
//                names[i][1] = cities.get(i).getPostalCode() != null ? cities.get(i).getPostalCode() : "";
//                names[i][2] = cities.get(i).getProvinceCode() != null ? cities.get(i).getProvinceCode() : "";
//            }
//        } else {
//            names = new String[0][0];
//        }
//
//        return names;
    };

    public static Route data_subcategories = (Request request, Response response) -> {

        String area = request.queryParams("area");

        String categoryIdentifier = request.queryParams("category");
        Category category = null;
        if ((area != null) && StringUtils.isNotBlank(area) && StringUtils.isNotBlank(categoryIdentifier)) {
            category = CategoryDao.loadCategoryByIdentifier(area, categoryIdentifier);
        }

        String text = request.queryParams("name");

        String stringToRemove = StringUtils.substringBetween(text, "(", ")");
        if (StringUtils.isNotBlank(stringToRemove)) {
            text = StringUtils.remove(text, "(" + stringToRemove + ")");
        }
        text = StringUtils.remove(text, "(");
        text = StringUtils.remove(text, ")");
        text = StringUtils.remove(text, "^");
        text = StringUtils.remove(text, "$");

        List<Subcategory> subcategories = SubcategoryDao.loadSubcategoryListBy(
                area,
                (category != null) ? category.getCode() : null,
                text,
                0,
                20
        );

        // list
        return subcategories;
    };

    ////////////
    // internals
    private static String normalizeText(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        // case
        String txt = StringUtils.lowerCase(text);

        // spacing (1st)
        txt = StringUtils.trim(txt);
        txt = StringUtils.normalizeSpace(txt);

        // command
        txt = StringUtils.remove(txt, "\r");
        txt = StringUtils.remove(txt, "\t");

        // non alphabetic
        txt = StringUtils.replace(txt, ".", " ");
        txt = StringUtils.replace(txt, ",", " ");
        txt = StringUtils.replace(txt, ":", " ");
        txt = StringUtils.replace(txt, ";", " ");
        txt = StringUtils.replace(txt, "+", " ");
        txt = StringUtils.replace(txt, "-", " ");
        txt = StringUtils.replace(txt, "&", " ");
        txt = StringUtils.replace(txt, "\"", " ");

        // useless
        txt = StringUtils.replace(txt, " di ", " ");
        txt = StringUtils.replace(txt, " a ", " ");
        txt = StringUtils.replace(txt, " da ", " ");
        txt = StringUtils.replace(txt, " in ", " ");
        txt = StringUtils.replace(txt, " con ", " ");
        txt = StringUtils.replace(txt, " su ", " ");
        txt = StringUtils.replace(txt, " per ", " ");
        txt = StringUtils.replace(txt, " x ", " ");
        txt = StringUtils.replace(txt, " tra ", " ");
        txt = StringUtils.replace(txt, " fra ", " ");

        txt = StringUtils.replace(txt, " e ", " ");
        txt = StringUtils.replace(txt, " o ", " ");

        txt = StringUtils.replace(txt, " il ", " ");
        txt = StringUtils.replace(txt, " lo ", " ");
        txt = StringUtils.replace(txt, " l' ", " ");
        txt = StringUtils.replace(txt, " la ", " ");
        txt = StringUtils.replace(txt, " i ", " ");
        txt = StringUtils.replace(txt, " gli ", " ");
        txt = StringUtils.replace(txt, " le ", " ");

        txt = StringUtils.replace(txt, " un ", " ");
        txt = StringUtils.replace(txt, " uno ", " ");
        txt = StringUtils.replace(txt, " una ", " ");
        txt = StringUtils.replace(txt, " un'", " ");

        txt = StringUtils.replace(txt, "compro", " ");
        txt = StringUtils.replace(txt, "compra", " ");
        txt = StringUtils.replace(txt, "acquisto", " ");
        txt = StringUtils.replace(txt, "acquista", " ");
        txt = StringUtils.replace(txt, "vendo", " ");
        txt = StringUtils.replace(txt, "vende", " ");
        txt = StringUtils.replace(txt, "vendita", " ");
        txt = StringUtils.replace(txt, "cerco", " ");
        txt = StringUtils.replace(txt, "cerca", " ");

        // spacing (2nd)
        txt = StringUtils.trim(txt);
        txt = StringUtils.normalizeSpace(txt);

        text = txt;
        return text;
    }

    private static Map<String, Double> getScore(DocumentCategorizerME documentCategorizerME, String text) throws Exception {
        Map<String, Double> scoreMap = new HashMap<>();
        double[] categorize = documentCategorizerME.categorize(StringUtils.split(text));

        int catSize = documentCategorizerME.getNumberOfCategories();
        for (int i = 0; i < catSize; i++) {
            String category = documentCategorizerME.getCategory(i);
            scoreMap.put(category, categorize[documentCategorizerME.getIndex(category)]);
        }
        return scoreMap;

    }

    private static Category category(List<Category> categories, String categoryKey) {
        if (categories == null) {
            return null;
        }
        if (categories.isEmpty()) {
            return null;
        }
        if (StringUtils.isBlank(categoryKey)) {
            return null;
        }

        Category cat = null;
        for (Category category : categories) {

            String key = category.getTitle();
            key = StringUtils.lowerCase(key);
            key = StringUtils.trim(key);
            key = StringUtils.remove(key, " ");

            if (StringUtils.equalsIgnoreCase(categoryKey, key)) {
                cat = category;
                break;
            }

        }
        return cat;
    }

}
