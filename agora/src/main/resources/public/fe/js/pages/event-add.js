var autocomplete, fakeIds = [];
(function () {
    // Definisci un array di elementi precaricati
    var pagesRelated = $('#pageInitials').text();
    var initialData;
    if (pagesRelated) {
        initialData = JSON.parse(pagesRelated);
    }

    var selected = [];
    var initials = [];
    fakeIds = [];
    var newTags = new Set(); // Per tenere traccia dei nuovi tag aggiunti

    // Inizializza i dati iniziali
    for (var s in initialData) {
        if (initialData[s].isFake === "true") {
            fakeIds.push(initialData[s].text);
        }
        initials.push({id: initialData[s].id, text: initialData[s].text});
        selected.push(initialData[s].id);
        $('#selected-pages-container').append('<div class="tag" data-id="' + initialData[s].id + '">' + initialData[s].text + '</div>');
    }

    // Inizializza Select2
    $('#pageIds').select2({
        minimumInputLength: 3,
        data: initials,
        ajax: {
            url: $('#dataPagesUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (params) {
                updatePageIdsColor();
                return {
                    name: params.term /*toCamelCase(params.term)*/,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                updatePageIdsColor();
                params.page = params.page || 1;

                var results = [];
                if (data) {
                    results = data.map(function (item) {
                        return {
                            id: item[0],
                            text: item[1],
                            avatar: item[2],
                            descr: item[4]
                        };
                    });
                }

                // Aggiungi sempre l'opzione per creare un nuovo elemento
                if (params.term && !newTags.has(params.term.toLowerCase())) {
                    results.unshift({
                        id: 'fake_' + toCamelCase(params.term),
                        text: toCamelCase(params.term),
                        isCreate: true
                    });
                }

                return {
                    results: results,
                    pagination: {
                        more: data && data[0] && data[0][5] ? (params.page * 10) < data[0][5] : false
                    }
                };
            },
            cache: true
        },
        createTag: function (params) {
            // Verifica se il tag esiste già
            if (newTags.has(params.term.toLowerCase())) {
                return null; // Non creare duplicati
            }
            return {
                id: 'fake_' + toCamelCase(params.term), // Usa timestamp per ID unico
                text: toCamelCase(params.term),
                isNew: true
            };
        },
        templateResult: formatRepo,
        templateSelection: formatRepoSelection,
        escapeMarkup: function (markup) {
            return markup;
        }
    }).on('select2:unselect', function (e) {
        var data = e.params.data;
        if (data && data.text) {
            newTags.delete(data.text.toLowerCase());
        }
        setTimeout(function () {
            updatePageIdsColor();
        }, 50);
    });

    function formatRepo(repo) {
        if (repo.loading) {
            return repo.text;
        }

        // Se è un'opzione "Crea"
        if (repo.isCreate || repo.isNew) {
            return $(
                    '<div class="select2-result-repository clearfix d-flex align-items-center create-option">' +
                    '<div class="select2-result-repository__avatar"><i class="fa-2x bi bi-file-plus"></i></div>' +
                    '<div class="select2-result-repository__meta">' +
                    '<div class="select2-result-repository__title">Crea "' + repo.text + '"</div>' +
                    '</div>' +
                    '</div>'
                    );
        } else {
            var imgUrl = "https://agor.app/fe/images/avatar/placeholder.jpg";
            if (repo.avatar) {
                imgUrl = $('#imageSearchUri').attr('href') + repo.avatar;
            }

            var $container = $(
                    '<div class="select2-result-repository clearfix d-flex align-items-center">' +
                    '<div class="select2-result-repository__avatar"><img src="' + imgUrl + '" /></div>' +
                    '<div class="select2-result-repository__meta">' +
                    '<div class="select2-result-repository__title">' + repo.text + '</div>' +
                    "<div class='select2-result-repository__description' style='font-size: small; color: gray;'></div>" +
                    '</div>' +
                    '</div>'
                    );

            if (repo.descr) {
                var shortDesc = repo.descr.length > 50 ? repo.descr.substring(0, 50) + "..." : repo.descr;
                $container.find(".select2-result-repository__description").text(shortDesc);
            }
            return $container;

        }

    }

    function formatRepoSelection(repo) {
        return repo.text;
    }

    // Gestione eventi di selezione e deselezione
    $('#pageIds').on('select2:select', function (e) {
        var data = e.params.data;

        if (data.isNew || data.isCreate) {
            var tagText = data.text;
            var tempId = data.id || 'fake_' + tagText;
            tempId = CSS.escape(tempId);

            newTags.add(tagText.toLowerCase());
            // Aggiungi immediatamente il tag con un indicatore di caricamento
            $('#selected-pages-container').append(
                    '<div class="tag loading" data-id="' + tempId + '">' + tagText + '</div>'
                    );

            // Qui puoi aggiungere la chiamata AJAX per creare effettivamente l'elemento
            // Per ora lo simuliamo
            fakeIds.push(tagText);

            // Se l'opzione non esiste già nel select, aggiungila
            if (!$('#pageIds').find("option[value='" + tempId + "']").length) {
                var newOption = new Option(tagText, tempId, true, true);
                $('#pageIds').append(newOption).trigger('change');
            }

            setTimeout(function () {
                updatePageIdsColor();
            }, 50);
        } else {
            // Gestione normale per elementi esistenti
            $('#selected-pages-container').append(
                    '<div class="tag" data-id="' + data.id + '">' + data.text + '</div>'
                    );
            setTimeout(function () {
                updatePageIdsColor();
            }, 50);
        }
    }).on('select2:unselect', function (e) {
        var data = e.params.data;
        if (data) {
            $('#selected-pages-container .tag[data-id="' + data.id + '"]').remove();
            setTimeout(function () {
                updatePageIdsColor();
            }, 50);
        }
    });

    // Imposta i valori iniziali
    $('#pageIds').val(selected).trigger('change');

    $('#parentId').select2({
        minimumInputLength: 3,
        ajax: {
            url: $('#dataEventsUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    name: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                updatePageIdsColor();
                params.page = params.page || 1;

                var results = [];
                if (data) {
                    results = data.map(function (item) {
                        return {
                            id: item[0],
                            text: item[1],
                            avatar: item[2]
                        };
                    });
                }

                return {
                    results: results,
                    pagination: {
                        more: data && data[0] && data[0][4] ? (params.page * 10) < data[0][4] : false
                    }
                };
            },
            cache: true
        },
        createTag: function (params) {
            return null; // Non creare duplicati
        },
        templateResult: formatRepo,
        templateSelection: formatRepoSelection,
        escapeMarkup: function (markup) {
            return markup;
        }
    });

    $('#tags').select2({
        minimumInputLength: 2,
        tags: true,
        ajax: {
            url: $('#dataEventTagUri').attr('href'),
            dataType: 'json',
            delay: 250,
            quietMillis: 250,
            data: function (term) {
                return {name: term};
            },
            processResults: function (data) {
                var results;
                results = [];
                $.each(data, function (idx, item) {
                    results.push({
                        'id': item.toLowerCase(),
                        'text': item.toLowerCase()
                    });
                });
                return {results: results};
            }
        }
    }).on("select2:selecting", function (e) {
        // Converte il nuovo valore in lowercase prima di selezionarlo
        e.params.args.data.text = e.params.args.data.text.toLowerCase();
        e.params.args.data.id = e.params.args.data.id.toLowerCase();
    });

    $(window).keydown(function (event) {
        if (event.keyCode == 13) {
            event.preventDefault();
            return false;
        }
    });

    $('textarea').keydown(function (event) {
        if (event.keyCode == 13 && !event.shiftKey) {
            event.preventDefault();
            var content = this.value;
            var caret = getCaret(this);
            this.value = content.substring(0, caret) + "\n" + content.substring(caret, content.length);
            this.scrollTop = this.scrollHeight;
        }
    });

    var summerLang = 'en-US';
    if ($('#language').val() === 'it') {
        summerLang = 'it-IT';
    }
    $('.summernote').summernote({
        lang: summerLang,
        callbacks: {
            onPaste: function (e) {
                var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                e.preventDefault();
                document.execCommand('insertText', false, bufferText);
            }
        },
        toolbar: [
            ['style', ['bold', 'italic']], // Grassetto e Italico
            ['fontsize', ['fontsize']], // Dimensione del testo
            ['para', ['ul', 'ol']], // Liste (puntate e numerate)
            ['insert', ['link']] // Inserimento di link
        ],
        height: 300, // Altezza dell'editor
        disableLinkTarget: false, // Permette di aprire link in una nuova finestra
        popover: {
            image: [], link: [], air: [] // Disabilita i popover non necessari
        }
    });

    // Funzione per ottenere la posizione del cursore all'interno di un textarea
    function getCaret(el) {
        if (el.selectionStart) {
            return el.selectionStart;
        } else if (document.selection) {
            el.focus();

            var r = document.selection.createRange();
            if (r == null) {
                return 0;
            }

            var re = el.createTextRange(),
                    rc = re.duplicate();
            re.moveToBookmark(r.getBookmark());
            rc.setEndPoint('EndToStart', re);

            return rc.text.length;
        }
        return 0;
    }


    // Verifica il valore iniziale al caricamento della pagina
    checkCountryCode();

    // Aggiungi un gestore per l'evento change sull'elemento con id "countrycode"
    $('#countryCode').on('change', function () {
        checkCountryCode();
    });

    $('#form-event-add').submit(function (event) {
        event.preventDefault();
        // Validation rules
        if ($("#form-event-add").valid()) {
            // post to url
            var url = $("#eventAddSaveUri").attr("href");
            var data = new FormData($(this)[0]);
            // nuova gestione isContainer
            if ($('#isContainer').val() === 'true') {
                data.append('type', 'container');
            }
            if ($("#parentId").attr('disabled')) {
                data.append('parentId', $('#parentId').val());
            }

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();
                            var url = $("#backUri").attr("href");
                            if (!url) {
                                url = $("#accountCalendarUri").attr("href");
                            }
                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: label('common.event.created'),
                                showConfirmButton: false,
                                timer: 1500
                            }).then(function () {
                                window.location.href = url;
                            });
                        },
                error:
                        function (response, status, errorThrown) {
                            var msg = label('common.insertion.failed');
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            $.unblockUI();
                            // warn
                            Swal.fire({
                                position: 'top',
                                icon: 'error',
                                title: label('common.ops.error'),
                                text: msg,
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: label('common.continue'),
                                // todo
                                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                            });
                        }
            });
        }
        return false;
    });

    // Drop Uploader
    // ------------------------------
    $('input[attachment=true]').drop_uploader({
        uploader_text: $('#uploader-text').text(),
        browse_text: 'browse',
        browse_css_class: '',
        browse_css_selector: 'file_browse',
        uploader_icon: '<i class="fa fa-upload fa-2x"></i>',
        file_icon: '<i class="fa fa-file-o"></i>',
        time_show_errors: 5,
        layout: 'list',
        method: 'normal',
        url: '',
        delete_url: ''
    });

    $("#startDate").on("change", function () {
        checkEventDate();
    });
    $("#startHour").on("change", function () {
        checkEventDate();
    });

    initAutocomplete();
    setTimeout(function () {
        updatePageIdsColor();
    }, 50);
    checkEventDate();

    if ($('#isContainer').val() === 'true') {
        $(".hide-for-container").hide();
        // remove also required attribute
        $('.hide-for-container').find('[required]').prop('required', false);
    }
})();

function checkEventDate() {
    var startDate = $("#startDate").val();
    var startHour = $("#startHour").val() || "00:00";
    if (isBeforeNow(startDate, startHour)) {
        $("#passed-event-warning").removeClass("d-none");
    } else {
        $("#passed-event-warning").addClass("d-none");
    }
}

function isBeforeNow(dateStr, timeStr = "00:00") {
    // Parse the date string (dd/mm/yyyy)
    const [day, month, year] = dateStr.split('/').map(Number);

    // Parse the time string (HH:mm)
    const [hours, minutes] = timeStr.split(':').map(Number);

    // Create the full Date object with date and time
    const inputDate = new Date(year, month - 1, day, hours, minutes);

    // Get the current date and time
    const now = new Date();

    return inputDate < now;
}

function updatePageIdsColor() {
    // coloro di rosso le fakePageId così per renderlo chiaro
    if (fakeIds.length > 0) {
        fakeIds.forEach(function (title) {
            var escapedTitle = CSS.escape(title);
            $(".select2-selection__choice[title='" + title + "']").addClass("text-danger");
            $(".select2-selection__choice[title='" + title + "']").find("span").addClass("text-danger");
        });
    }
}

function checkCountryCode() {
    var countryCodeValue = $('#countryCode').val();
    if (countryCodeValue !== 'IT') {
        $('#provinceDiv').hide();
        $('#provinceExtDiv').show();
        $('.provinceCodeIt').prop('name', 'provinceCodeIt').prop('id', 'provinceCodeIt');
        $('.provinceCodeIt').removeAttr("required");
        $('.provinceCodeExt').prop('name', 'provinceCode').prop('id', 'provinceCode');
    } else {
        $('#provinceDiv').show();
        $('#provinceExtDiv').hide();
        $('.provinceCodeIt').prop('name', 'provinceCode').prop('id', 'provinceCode');
        $('.provinceCodeIt').attr("required", "required");
        $('.provinceCodeExt').prop('name', 'provinceCodeExt').prop('id', 'provinceCodeExt');
    }
    $('.provinceCode').val('');
}

// async registering autocomplete address
function initAutocomplete() {
    (function () {
        var input = $('#fulladdress')[0];
        var options = {
            offset: 2
        };
        autocomplete = new google.maps.places.Autocomplete(input, options);
        autocomplete.addListener('place_changed', selectAddress);

        // hacking google logo
        setTimeout(function () {
            $('.pac-container').removeClass('pac-logo');
        }, 1000);
    })();
}

function selectAddress() {
    (function () {
        // get city
        var city = '';
        var address = '';
        var number = '';
        var postalCode = '';
        var countryCode = '';
        var provinceCode = '';
        var venue = '';
        var place = autocomplete.getPlace();
        if (place !== null) {
            if (typeof place.address_components !== 'undefined') {
                for (var i = 0; i < place.address_components.length; i++) {
                    var type = place.address_components[i].types[0];
                    if (type === 'locality') {
                        city = place.address_components[i]['long_name'];
                    }
                    if (type === 'route') {
                        address = place.address_components[i]['long_name'];
                    }
                    if (type === 'street_number') {
                        number = place.address_components[i]['long_name'];
                    }
                    if (type === 'postal_code') {
                        postalCode = place.address_components[i]['long_name'];
                    }
                    if (type === 'administrative_area_level_2') {
                        provinceCode = place.address_components[i]['short_name'];
                    }
                    if (type === 'country') {
                        countryCode = place.address_components[i]['short_name'];
                    }
                    //                  type for venue
                    //                "point_of_interest"
                    //                "establishment"
                    if (place.types.includes("point_of_interest") || place.types.includes("establishment")) {
                        venue = place.name;
                    }
                }
                if (address !== '') {
                    if (number !== '') {
                        address += ' ' + number;
                    }
                }
            }
        }
        $('#countryCode').val(countryCode).change();
        checkCountryCode();
        $('#city').val(city);
        $('#address').val(address);
        $('#postalCode').val(postalCode);
        $('#provinceCode').val(provinceCode).change();
    })();
}

function toCamelCase(str) {
    return str
            .split(' ') // Split the string into words
            .map((word, index) => {
                return word.charAt(0).toUpperCase() + word.slice(1); // Capitalize the first letter of subsequent words
            })
            .join(' '); // Join the words back into a single string
}