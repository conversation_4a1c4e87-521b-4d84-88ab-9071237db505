package com.agora.pojo;

import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Event extends Pojo {

    private String name;
    private String description;
    private String category;
    private String type;            // container, single

    private String status;

    //contacts
    private String phoneNumber;
    private String phoneNumberAdditional;
    private String email;

    // registration
    private String registrationUrl;
    private Date registrationDeadlineDate;

    //tickets
    private String ticketsUrl;
    private Double ticketsMinPrice;
    private Boolean freeEntry;

    //date
    private Date startDate;
    private String startHour;
    private Date endDate;
    private String endHour;
    private Date publicationDate;           // data di pubblicazione (usata specialmente per i container)

    private String performerType;
    private String performerName;

    // images
    private ObjectId coverImageId;

    private Boolean editorChoice;

    private String fulladdress;
    private String address;
    private String extraAddress;
    private String city;
    private String postalCode;
    private String provinceCode;
    private String countryCode;
    private String lat;
    private String lng;

    private List<String> fakePageIds;       // elenco pagine collegate all'evento che non esistono ancora
    private List<ObjectId> pageIds, initialPageIds;         // elenco pagine collegate all'evento
    private List<String> tags;              // elenco di tag

    private ObjectId userId;                // inserito da
    private ObjectId ownerId;               // proprietario pagina
    private ObjectId originalId;            // copiato da

    private String identifier;
    private String titleIdentifier;         // usato nel cerca
    private String qrcode;
    private ObjectId qrcodeFileId;
    private Boolean cancelled;

    private List<ObjectId> validPageIds;    //campo di appoggio per query
    private Long followers;                 // campo fake per mostrare un numero random di follower
    private ObjectId locandina;

    private ObjectId parentId;              // campo usato per definire se sono figlio di un evento "padre"
    private List<ObjectId> childIds;        // campo popolato in automatico che contiene tutti gli eventi figli
    private Boolean showFollowers;          // campo per attivare la visualizzazione dei follower

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumberAdditional() {
        return phoneNumberAdditional;
    }

    public void setPhoneNumberAdditional(String phoneNumberAdditional) {
        this.phoneNumberAdditional = phoneNumberAdditional;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRegistrationUrl() {
        return registrationUrl;
    }

    public void setRegistrationUrl(String registrationUrl) {
        this.registrationUrl = registrationUrl;
    }

    public Date getRegistrationDeadlineDate() {
        return registrationDeadlineDate;
    }

    public void setRegistrationDeadlineDate(Date registrationDeadlineDate) {
        this.registrationDeadlineDate = registrationDeadlineDate;
    }

    public String getTicketsUrl() {
        return ticketsUrl;
    }

    public void setTicketsUrl(String ticketsUrl) {
        this.ticketsUrl = ticketsUrl;
    }

    public Double getTicketsMinPrice() {
        return ticketsMinPrice;
    }

    public void setTicketsMinPrice(Double ticketsMinPrice) {
        this.ticketsMinPrice = ticketsMinPrice;
    }

    public Boolean getFreeEntry() {
        return freeEntry;
    }

    public void setFreeEntry(Boolean freeEntry) {
        this.freeEntry = freeEntry;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public String getStartHour() {
        return startHour;
    }

    public void setStartHour(String startHour) {
        this.startHour = startHour;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getEndHour() {
        return endHour;
    }

    public void setEndHour(String endHour) {
        this.endHour = endHour;
    }

    public Date getPublicationDate() {
        return publicationDate;
    }

    public void setPublicationDate(Date publicationDate) {
        this.publicationDate = publicationDate;
    }

    public String getPerformerType() {
        return performerType;
    }

    public void setPerformerType(String performerType) {
        this.performerType = performerType;
    }

    public String getPerformerName() {
        return performerName;
    }

    public void setPerformerName(String performerName) {
        this.performerName = performerName;
    }

    public ObjectId getCoverImageId() {
        return coverImageId;
    }

    public void setCoverImageId(ObjectId coverImageId) {
        this.coverImageId = coverImageId;
    }

    public Boolean getEditorChoice() {
        return editorChoice;
    }

    public void setEditorChoice(Boolean editorChoice) {
        this.editorChoice = editorChoice;
    }

    public String getFulladdress() {
        return fulladdress;
    }

    public void setFulladdress(String fulladdress) {
        this.fulladdress = fulladdress;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getExtraAddress() {
        return extraAddress;
    }

    public void setExtraAddress(String extraAddress) {
        this.extraAddress = extraAddress;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public List<String> getFakePageIds() {
        return fakePageIds;
    }

    public void setFakePageIds(List<String> fakePageIds) {
        this.fakePageIds = fakePageIds;
    }

    public List<ObjectId> getPageIds() {
        return pageIds;
    }

    public void setPageIds(List<ObjectId> pageIds) {
        this.pageIds = pageIds;
    }

    public List<ObjectId> getInitialPageIds() {
        return initialPageIds;
    }

    public void setInitialPageIds(List<ObjectId> initialPageIds) {
        this.initialPageIds = initialPageIds;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(ObjectId ownerId) {
        this.ownerId = ownerId;
    }

    public ObjectId getOriginalId() {
        return originalId;
    }

    public void setOriginalId(ObjectId originalId) {
        this.originalId = originalId;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getTitleIdentifier() {
        return titleIdentifier;
    }

    public void setTitleIdentifier(String titleIdentifier) {
        this.titleIdentifier = titleIdentifier;
    }

    public String getQrcode() {
        return qrcode;
    }

    public void setQrcode(String qrcode) {
        this.qrcode = qrcode;
    }

    public ObjectId getQrcodeFileId() {
        return qrcodeFileId;
    }

    public void setQrcodeFileId(ObjectId qrcodeFileId) {
        this.qrcodeFileId = qrcodeFileId;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public List<ObjectId> getValidPageIds() {
        return validPageIds;
    }

    public void setValidPageIds(List<ObjectId> validPageIds) {
        this.validPageIds = validPageIds;
    }

    public Long getFollowers() {
        return followers;
    }

    public void setFollowers(Long followers) {
        this.followers = followers;
    }

    public ObjectId getLocandina() {
        return locandina;
    }

    public void setLocandina(ObjectId locandina) {
        this.locandina = locandina;
    }

    public ObjectId getParentId() {
        return parentId;
    }

    public void setParentId(ObjectId parentId) {
        this.parentId = parentId;
    }

    public List<ObjectId> getChildIds() {
        return childIds;
    }

    public void setChildIds(List<ObjectId> childIds) {
        this.childIds = childIds;
    }

    public Boolean getShowFollowers() {
        return showFollowers;
    }

    public void setShowFollowers(Boolean showFollowers) {
        this.showFollowers = showFollowers;
    }

}
