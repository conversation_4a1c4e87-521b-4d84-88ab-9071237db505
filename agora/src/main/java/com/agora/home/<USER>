package com.agora.home;

import com.agora.commons.*;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CityDao;
import com.agora.dao.CustomerDao;
import com.agora.dao.CustomerNotificationDao;
import com.agora.dao.EntityNotificationDao;
import com.agora.dao.EventDao;
import com.agora.dao.PageDao;
import com.agora.dao.ProvinceDao;
import com.agora.dao.SearchlogDao;
import com.agora.message.MessageSender;
import com.agora.pojo.City;
import com.agora.pojo.Customer;
import com.agora.pojo.Event;
import com.agora.pojo.Page;
import com.agora.pojo.Province;
import com.agora.pojo.Searchlog;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.util.*;
import com.github.slugify.Slugify;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class HomeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(HomeController.class.getName());

    public static TemplateViewRoute wall = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null && !(StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()))) {

            customer = CustomerDao.loadCustomerByUserId(user.getId());
            attributes.put("customerEntry", CustomerCommons.toEntry(customer));

            Boolean existCustomerNotification = CustomerNotificationDao.existCustomerNotificationByUserId(user.getId());
            attributes.put("existCustomerNotification", existCustomerNotification);
        }

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("eventId"));


        if (eventId != null) {
            List<Event> wallEventList = new ArrayList<>();
            Event wallEvent = EventDao.loadEvent(eventId);
            if (wallEvent != null) {
                wallEventList.add(wallEvent);
            }
            if (user != null && !(StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()))) {
                attributes.put("wallEventList", EventCommons.toWallEntries(wallEventList, user.getId()));
            } else {
                attributes.put("wallEventList", EventCommons.toWallEntries(wallEventList));
            }
            
            // return Manager.render(Templates.ABOUT, attributes, RouteUtils.pathType(request));
        } else {
            // pagination
            int pagination = 12;
            int skip = NumberUtils.toInt(request.queryParams("skip"));
            int limit = NumberUtils.toInt(request.queryParams("limit"));
            if (limit <= 0) {
                limit = pagination;
            }
            attributes.put("skip", skip);
            attributes.put("limit", limit);
            attributes.put("pagination", pagination);

            List<Event> wallEventList;
            if (user != null && !(StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()))) {
                wallEventList = EventDao.loadWallEventList(user.getId(), skip > 0 ? skip + 1 : skip, limit + 1);
            } else {
                wallEventList = EventDao.loadRandomWallEventList(skip > 0 ? skip + 1 : skip, limit + 1);
            }


            // loadmore
            boolean loadmore = false;
            if ((wallEventList != null) && (!wallEventList.isEmpty())) {
                loadmore = wallEventList.size() > limit;
            }
            if ((wallEventList != null) && (!wallEventList.isEmpty())) {
                if (wallEventList.size() > limit) {
                    wallEventList = wallEventList.subList(0, limit);
                }
            }
            attributes.put("loadmore", loadmore);

            if (user != null && !(StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()))) {
                attributes.put("wallEventList", EventCommons.toWallEntries(wallEventList, user.getId()));
            } else {
                attributes.put("wallEventList", EventCommons.toWallEntries(wallEventList));
            }

            URIBuilder uriBuilder;
            try {
                if (RouteUtils.publicQueryString(request) != null) {
                    uriBuilder = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                    List<NameValuePair> queryParameters = uriBuilder.getQueryParams();
                    for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                        NameValuePair queryParameter = queryParameterItr.next();
                        if (queryParameter.getName().equals("skip")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("limit")) {
                            queryParameterItr.remove();
                        }
                    }
                    uriBuilder.setParameters(queryParameters);

                    String resultUrl = uriBuilder.build().toString();
                    attributes.put("resultUrl", resultUrl);
                } else {
                    attributes.put("resultUrl", RouteUtils.publicUrl(request));
                }
            } catch (URISyntaxException ex) {
                //
            }
        }

        return Manager.render(Templates.WALL, attributes, RouteUtils.pathType(request));

    };
    
    public static TemplateViewRoute home = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null && !(StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()))) {

            customer = CustomerDao.loadCustomerByUserId(user.getId());
            attributes.put("customerEntry", CustomerCommons.toEntry(customer));

            Boolean existCustomerNotification = CustomerNotificationDao.existCustomerNotificationByUserId(user.getId());
            attributes.put("existCustomerNotification", existCustomerNotification);
        }

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // load sponsor pages
        List<SponsorPageCommons.SponsorPageEntry> sponsorPageEntries = SponsorPageCommons.loadActiveSponsorPageEntries();
        List<ObjectId> sponsorPageIds = new ArrayList<>();
        List<Page> sponsorPages = new ArrayList<>();
        for (SponsorPageCommons.SponsorPageEntry entry : sponsorPageEntries) {
            sponsorPages.add(entry.getPage());
            sponsorPageIds.add(entry.getPage().getId());
        }
        // attributes.put("sponsorPageList", PageCommons.toEntries(sponsorPages));

        // load wall pages
        List<Page> pageList = PageDao.loadHomePages(sponsorPageIds);
        if (!sponsorPages.isEmpty() && pageList != null) {
            for (Page page : sponsorPages) {
                pageList.add(0, page);
            }
        }
        attributes.put("pageList", PageCommons.toEntries(pageList));

        // load sponsor events
        List<SponsorEventCommons.SponsorEventEntry> sponsorEventEntries = SponsorEventCommons.loadActiveSponsorEventEntries();
        List<ObjectId> sponsorEventIds = new ArrayList<>();
        List<Event> sponsorEvents = new ArrayList<>();
        for (SponsorEventCommons.SponsorEventEntry entry : sponsorEventEntries) {
            sponsorEvents.add(entry.getEvent());
            sponsorEventIds.add(entry.getEvent().getId());
        }
        // commentato per ora, aggiunti come eventi normali
        // attributes.put("sponsorEventList", EventCommons.toEntries(sponsorEvents));

        // load wall events
        List<Event> eventList = EventDao.loadHomeEvents(sponsorEventIds);
        if (eventList == null) {
            eventList = new ArrayList<>();
        }
        // add all the sponsored events at the start of the list
        if (!sponsorEvents.isEmpty()) {
            for (Event event : sponsorEvents) {
                eventList.add(0, event);
            }
        }
        attributes.put("eventList", EventCommons.toEntries(eventList));

        return Manager.render(Templates.HOME, attributes, RouteUtils.pathType(request));

    };

    public static TemplateViewRoute results = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
//            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
//                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
//            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
//            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
//            return Manager.renderEmpty();
        }

        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // text
        String text = ParamUtils.emptyToNull(request.queryParams("text"));
        attributes.put("text", TextUtils.toCamelCase(text));
        String q = ParamUtils.emptyToNull(request.queryParams("q"));
        attributes.put("q", q);


        // pagination
        int pagination = 12;
        int skip = NumberUtils.toInt(request.queryParams("skip"));
        int limit = NumberUtils.toInt(request.queryParams("limit"));
        if (limit <= 0) {
            limit = pagination;
        }
        attributes.put("skip", skip);
        attributes.put("limit", limit);
        attributes.put("pagination", pagination);

        if (StringUtils.isBlank(q)) {
            q = "all";
        }

        if (skip == 0 && StringUtils.isNotBlank(text)) {
            logSearch(request, user, token, text, q);
        }

        if (StringUtils.equalsIgnoreCase(q, "all")) {
            skip = (skip / 2);
            limit = (limit / 2);
            List<Page> pageList = PageDao.loadPageListBy(text, skip, limit + 1);

            // loadmore
            boolean loadmore = false;
            if ((pageList != null) && (!pageList.isEmpty())) {
                loadmore = pageList.size() > limit;
            }
            if ((pageList != null) && (!pageList.isEmpty())) {
                if (pageList.size() > limit) {
                    pageList = pageList.subList(0, limit);
                }
            }

            attributes.put("pageList", PageCommons.toEntries(pageList));
            
            List<Event> eventList = EventDao.loadEventListBy(text, skip, limit + 1);

            if (!loadmore) {
                if ((eventList != null) && (!eventList.isEmpty())) {
                    loadmore = eventList.size() > limit;
                }
            }
            attributes.put("loadmore", loadmore);
            
            if ((eventList != null) && (!eventList.isEmpty())) {
                if (eventList.size() > limit) {
                    eventList = eventList.subList(0, limit);
                }
            }
            attributes.put("eventList", EventCommons.toEntries(eventList));
            

        } else if (StringUtils.equalsIgnoreCase(q, "pages")) {
            List<Page> pageList = PageDao.loadPageListBy(text, skip, limit + 1);

            // loadmore
            boolean loadmore = false;
            if ((pageList != null) && (!pageList.isEmpty())) {
                loadmore = pageList.size() > limit;
            }
            if ((pageList != null) && (!pageList.isEmpty())) {
                if (pageList.size() > limit) {
                    pageList = pageList.subList(0, limit);
                }
            }
            attributes.put("loadmore", loadmore);

            attributes.put("pageList", PageCommons.toEntries(pageList));
        } else if (StringUtils.equalsIgnoreCase(q, "events")) {
            List<Event> eventList = EventDao.loadEventListBy(text, skip, limit + 1);

            // loadmore
            boolean loadmore = false;
            if ((eventList != null) && (!eventList.isEmpty())) {
                loadmore = eventList.size() > limit;
            }
            if ((eventList != null) && (!eventList.isEmpty())) {
                if (eventList.size() > limit) {
                    eventList = eventList.subList(0, limit);
                }
            }
            attributes.put("loadmore", loadmore);

            attributes.put("eventList", EventCommons.toEntries(eventList));
        } else {
            return Manager.render(Templates.HOME, attributes, RouteUtils.pathType(request));
        }

        URIBuilder uriBuilder;
        try {
            if (RouteUtils.publicQueryString(request) != null) {
                uriBuilder = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                List<NameValuePair> queryParameters = uriBuilder.getQueryParams();
                for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                    NameValuePair queryParameter = queryParameterItr.next();
                    if (queryParameter.getName().equals("skip")) {
                        queryParameterItr.remove();
                    }
                    if (queryParameter.getName().equals("limit")) {
                        queryParameterItr.remove();
                    }
                }
                uriBuilder.setParameters(queryParameters);

                String resultUrl = uriBuilder.build().toString();
                attributes.put("resultUrl", resultUrl);
            } else {
                attributes.put("resultUrl", RouteUtils.publicUrl(request));
            }
        } catch (URISyntaxException ex) {
            //
        }

        return Manager.render(Templates.RESULTS, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute events = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("eventId"));

        if (eventId != null) {
            List<Event> eventList = new ArrayList<>();
            Event wallEvent = EventDao.loadEvent(eventId);
            if (wallEvent != null) {
                eventList.add(wallEvent);
            }

            if (user != null && !(StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()))) {
                attributes.put("eventList", EventCommons.toWallEntries(eventList, user.getId()));
            } else {
                attributes.put("eventList", EventCommons.toWallEntries(eventList));
            }

        } else {
            String selectedTab = "";
            if (StringUtils.isNotBlank(request.queryParams("tab"))) {
                selectedTab = request.queryParams("tab");
            }

            if (StringUtils.isBlank(selectedTab) || StringUtils.equalsIgnoreCase(selectedTab, "all")) {
                // selectedCity
                String selectedCity = null;
                String selectedCityFromParams = null;
                if (StringUtils.isNotBlank(request.params("city"))) {
                    selectedCity = request.params("city");
                }
                if (StringUtils.equalsIgnoreCase(selectedCity, Defaults.CITY_IDENTIFIER_ITALY)) {
                    selectedCity = null;
                }
                if (StringUtils.isNotBlank(request.queryParams("city"))) {
                    selectedCityFromParams = request.queryParams("city");
                }

                List<String> tagList = EventDao.loadEventFutureTagList();
                attributes.put("tagList", tagList);
                
                // city
                City city = null;
                Province province = null;
                if (StringUtils.isNotBlank(selectedCity)) {
                    if (StringUtils.contains(selectedCity, "-e-provincia")) {
                        String provinceStr = StringUtils.substringBefore(selectedCity, "-e-provincia");
                        City provinceCity = CityDao.loadCityByIdentifier(provinceStr);
                        if (provinceCity != null) {
                            province = ProvinceDao.loadProvinceByDescription(provinceCity.getCity());
                            if (province != null) {
                                city = new City();
                                city.setCity(province.getDescription() + " e provincia");
                                city.setProvinceCode(province.getCode());
                                city.setPostalCode("ZZZZZ");
                                city.setRegion("ZZZZZ");
                                Slugify slg = new Slugify();
                                city.setIdentifier(slg.slugify(city.getCity()));
                            }
                        }
                    } else {
                        city = CityDao.loadCityByIdentifier(selectedCity);
                    }
                    attributes.put("city", city);
                }

                attributes.put("selectedCity", selectedCity);

                // block on missing city
                //if (!StringUtils.equalsIgnoreCase(request.params("city"), Defaults.CITY_IDENTIFIER_ITALY) && (city == null)) {
                    // simulate a not found page (DO NOT USE Spark.halt becouse deny notFound route rendering)
                //    return null;
                //}

                // categories
                String[] selectedCategories = null;
                if (StringUtils.isNotBlank(request.params("category"))) {
                    selectedCategories = new String[] {request.params("category")};
                }
                attributes.put("selectedCategories", selectedCategories);
                
                String tag = null;
                if (StringUtils.isNotBlank(request.queryParams("tag"))) {
                    tag = request.queryParams("tag");
                }
                attributes.put("tag", tag);

                // block on missing category
                if (StringUtils.isNotBlank(request.params("category"))) {
                    String validCategories =
                            "cultura" + "|" +
                            "sport" + "|" +
                            "intrattenimento" + "|" +
                            "cibo" + "|" +
                            "benessere" + "|" +
                            "business" + "|" +
                            "comunita" + "|" +
                            ""
                            ;
                    if (!StringUtils.containsIgnoreCase(validCategories, request.params("category") + "|")) {
                        // simulate a not found page (DO NOT USE Spark.halt becouse deny notFound route rendering)
                        return null;
                    }
                }


                int pagination = 12;
                int skip = NumberUtils.toInt(request.queryParams("skip"));
                int limit = NumberUtils.toInt(request.queryParams("limit"));
                if (limit <= 0) {
                    limit = pagination;
                }
                attributes.put("skip", skip);
                attributes.put("limit", limit);
                attributes.put("pagination", pagination);

                Date fromDate = TimeUtils.beginOfDay(new Date());
                Date toDate = DateUtils.addDays(TimeUtils.beginOfDay(new Date()), 365);
                if (StringUtils.isNoneEmpty(request.queryParams("startDate"))) {
                    fromDate = TimeUtils.beginOfDay(TimeUtils.toDate(request.queryParams("startDate")));
                }
                if (StringUtils.isNoneEmpty(request.queryParams("endDate"))) {
                    toDate = TimeUtils.endOfDay(TimeUtils.toDate(request.queryParams("endDate")));
                }
                attributes.put("fromDate", fromDate);
                attributes.put("toDate", toDate);


                List<Event> eventList = null;
                String cityName = null;
                if (city != null) {
                    cityName = city.getCity();
                } else if (StringUtils.isNotBlank(selectedCityFromParams)) {
                    cityName = selectedCityFromParams;
                }
                try {
                    eventList = EventDao.loadEventListBy(selectedCategories, cityName, fromDate, toDate, skip, limit + 1, null, province, tag);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }

                // loadmore
                boolean loadmore = false;
                if ((eventList != null) && (!eventList.isEmpty())) {
                    loadmore = eventList.size() > limit;
                }
                if ((eventList != null) && (!eventList.isEmpty())) {
                    if (eventList.size() > limit) {
                        eventList = eventList.subList(0, limit);
                    }
                }

                if (user != null && !(StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()))) {
                    attributes.put("eventList", EventCommons.toWallEntries(eventList, user.getId()));
                } else {
                    attributes.put("eventList", EventCommons.toWallEntries(eventList));
                }

                attributes.put("loadmore", loadmore);

                URIBuilder uriBuilder;
                try {
                    if (RouteUtils.publicQueryString(request) != null) {
                        uriBuilder = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                        List<NameValuePair> queryParameters = uriBuilder.getQueryParams();
                        for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                            NameValuePair queryParameter = queryParameterItr.next();
                            if (queryParameter.getName().equals("skip")) {
                                queryParameterItr.remove();
                            }
                            if (queryParameter.getName().equals("tab")) {
                                queryParameterItr.remove();
                            }
                            if (queryParameter.getName().equals("limit")) {
                                queryParameterItr.remove();
                            }
                        }
                        uriBuilder.setParameters(queryParameters);

                        String resultUrl = uriBuilder.build().toString();
                        attributes.put("resultUrl", resultUrl);
                    } else {
                        attributes.put("resultUrl", RouteUtils.publicUrl(request));
                    }
                } catch (URISyntaxException ex) {
                    //
                }
            }

            if (StringUtils.isBlank(selectedTab) || StringUtils.equalsIgnoreCase(selectedTab, "follow")) {
                if (user != null && !(StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()))) {
                    int paginationFollow = 12;
                    int skipFollow = NumberUtils.toInt(request.queryParams("skipFollow"));
                    int limitFollow = NumberUtils.toInt(request.queryParams("limitFollow"));
                    if (limitFollow <= 0) {
                        limitFollow = paginationFollow;
                    }
                    attributes.put("skipFollow", skipFollow);
                    attributes.put("limitFollow", limitFollow);
                    attributes.put("paginationFollow", paginationFollow);


                    List<Event> eventListFollow = null;
                    try {
                        eventListFollow = EventDao.loadFollowEventList(user.getId(), skipFollow, limitFollow + 1);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }

                    // loadmore
                    boolean loadmoreFollow = false;
                    if ((eventListFollow != null) && (!eventListFollow.isEmpty())) {
                        loadmoreFollow = eventListFollow.size() > limitFollow;
                    }
                    if ((eventListFollow != null) && (!eventListFollow.isEmpty())) {
                        if (eventListFollow.size() > limitFollow) {
                            eventListFollow = eventListFollow.subList(0, limitFollow);
                        }
                    }


                    attributes.put("eventListFollow", EventCommons.toWallEntries(eventListFollow, user.getId()));
                    attributes.put("loadmoreFollow", loadmoreFollow);

                    URIBuilder uriBuilderFollow;
                    try {
                        if (RouteUtils.publicQueryString(request) != null) {
                            uriBuilderFollow = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                            List<NameValuePair> queryParameters = uriBuilderFollow.getQueryParams();
                            for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                                NameValuePair queryParameter = queryParameterItr.next();
                                if (queryParameter.getName().equals("skipFollow")) {
                                    queryParameterItr.remove();
                                }
                                if (queryParameter.getName().equals("limitFollow")) {
                                    queryParameterItr.remove();
                                }
                                if (queryParameter.getName().equals("tab")) {
                                    queryParameterItr.remove();
                                }
                            }
                            uriBuilderFollow.setParameters(queryParameters);

                            String resultUrlFollow = uriBuilderFollow.build().toString();
                            attributes.put("resultUrlFollow", resultUrlFollow);
                        } else {
                            attributes.put("resultUrlFollow", RouteUtils.publicUrl(request));
                        }
                    } catch (URISyntaxException ex) {
                        //
                    }
                }

            }

        }

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.EVENTS, attributes, RouteUtils.pathType(request));
    };

    public static Route notification_all_read = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        EntityNotificationDao.updateEntityNotificationAllRead(user.getId());

        return "ok";
    };

    public static Route notification_read = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        ObjectId entityNotificationId = ParamUtils.toObjectId(request.queryParams("entityNotificationId"));

        if (entityNotificationId != null) {
            EntityNotificationDao.updateEntityNotificationRead(entityNotificationId);
        }

        return "ok";
    };

    public static TemplateViewRoute about = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.ABOUT, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute mediakit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));
        attributes.put("baseUrl", RouteUtils.baseUrl(request).replace(RouteUtils.contextPath(request), ""));

        return Manager.render(Templates.MEDIAKIT, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute tos = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.TOS, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute sitemap = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.SITEMAP, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute contacts = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.CONTACTS, attributes, RouteUtils.pathType(request));
    };

    public static Route contacts_send = (Request request, Response response) -> {
        // params
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        String name = null;
        String email = null;
        String subject = null;
        String message = null;
        String recaptchaToken = null;
        for (FileItem field : fields) {
            if (field.isFormField()) {
                switch (field.getFieldName()) {
                    case "name":
                        name = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "email":
                        email = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "subject":
                        subject = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "message":
                        message = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "g-recaptcha-response":
                        recaptchaToken = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    default:
                        LOGGER.warn("received unknown field " + field.getFieldName());
                        break;
                }
            } else {
                // posted files
                switch (field.getFieldName()) {
                    default:
                        LOGGER.warn("received unknown file field " + field.getFieldName());
                        break;
                }
            }
        }

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // language
        String language = RouteUtils.language(request);

        if (!MessageSender.validEmailAddress(email)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        if (RecaptchaUtils.isValid(recaptchaToken)) {
            if (!NotificationCommons.notifyContact(request, email, name, subject, message, language)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // status required
        return "1";
    };

    public static void logSearch(Request request, User user, String token, String key, String type) {

        // user
        if (user == null) {
            return;
        }
        if (user.getId() == null) {
            return;
        }

        // ip
        String ip = RouteUtils.getIp(request);
        if (StringUtils.isBlank(ip)) {
            return;
        }

        // token
        if (StringUtils.isBlank(token)) {
            return;
        }

        Searchlog searchlog = new Searchlog();
        searchlog.setUserId(user.getId());
        searchlog.setKey(key);
        searchlog.setType(type);
        searchlog.setIp(ip);
        searchlog.setToken(token);
        searchlog.setProfile(user.getProfileType());

        try {
            SearchlogDao.insertSearchlog(searchlog);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

    }
}
