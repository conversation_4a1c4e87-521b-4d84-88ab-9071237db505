var autocomplete, fakeIds = [];
$(function () {    
    // Definisci un array di elementi precaricati
    var pagesRelated = $('#pageInitials').text();
    var initialData;
    if (pagesRelated) {
        initialData = JSON.parse(pagesRelated);
    }

    var selected = [];
    var initials = [];
    fakeIds = [];
    var newTags = new Set();

    // Inizializza i dati iniziali
    for (var s in initialData) {
        if (initialData[s].isFake === "true") {
            fakeIds.push(initialData[s].text);
        }
        initials.push({id: initialData[s].id, text: initialData[s].text});
        selected.push(initialData[s].id);
        $('#selected-pages-container').append('<div class="tag" data-id="' + initialData[s].id + '">' + initialData[s].text + '</div>');
    }
    
    
    $('#pageIds').select2({
        minimumInputLength: 3,
        data: initials,
        ajax: {
            url: $('#dataPagesUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (params) {
                updatePageIdsColor();
                return {
                    name: params.term /*toCamelCase(params.term)*/,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                updatePageIdsColor();
                params.page = params.page || 1;

                var results = [];
                if (data) {
                    results = data.map(function(item) {
                        return {
                            id: item[0],
                            text: item[1],
                            avatar: item[2],
                            descr: item[4]
                        };
                    });
                }

                // Aggiungi sempre l'opzione per creare un nuovo elemento
                if (params.term && !newTags.has(params.term.toLowerCase())) {
                    results.unshift({
                        id: 'fake_' + toCamelCase(params.term),
                        text: toCamelCase(params.term),
                        isCreate: true
                    });
                }

                return {
                    results: results,
                    pagination: {
                        more: data && data[0] && data[0][5] ? (params.page * 10) < data[0][5] : false
                    }
                };
            },
            cache: true
        },
        createTag: function (params) {
            // Verifica se il tag esiste già
            if (newTags.has(params.term.toLowerCase())) {
                return null; // Non creare duplicati
            }
            return {
                id: 'fake_' + toCamelCase(params.term), // Usa timestamp per ID unico
                text: toCamelCase(params.term),
                isNew: true
            };
        },
        templateResult: formatRepo,
        templateSelection: formatRepoSelection,
        escapeMarkup: function (markup) {
            return markup;
        }
    });

    function formatRepo(repo) {
        if (repo.loading) {
            return repo.text;
        }

        // Se è un'opzione "Crea"
        if (repo.isCreate || repo.isNew) {
            return $(
                '<div class="select2-result-repository clearfix d-flex align-items-center create-option">' +
                '<div class="select2-result-repository__avatar"><i class="fa-2x bi bi-file-plus"></i></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">Crea "' + repo.text + '"</div>' +
                '</div>' +
                '</div>'
            );
        } else {
            var imgUrl = "https://agor.app/fe/images/avatar/placeholder.jpg";
            if (repo.avatar) {
                imgUrl = $('#imageUri').attr('href') + repo.avatar;
            }

            var $container = $(
                '<div class="select2-result-repository clearfix d-flex align-items-center">' +
                '<div class="select2-result-repository__avatar"><img src="' + imgUrl + '" /></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">' + repo.text + '</div>' +
                "<div class='select2-result-repository__description' style='font-size: small; color: gray;'></div>" +
                '</div>' +
                '</div>'
            );
            
            if (repo.descr) {
                var shortDesc = repo.descr.length > 50 ? repo.descr.substring(0, 50) + "..." : repo.descr;
                $container.find(".select2-result-repository__description").text(shortDesc);
            }
            return $container;
        }

    }
    
    function formatRepoSelection(repo) {
        return repo.text;
    }
    
// Gestione eventi di selezione e deselezione
    $('#pageIds').on('select2:select', function(e) {
        var data = e.params.data;

        if (data.isNew || data.isCreate) {
            var tagText = data.text;
            var tempId = data.id || 'fake_' + tagText;
            tempId = CSS.escape(tempId);

            newTags.add(tagText.toLowerCase());
            // Aggiungi immediatamente il tag con un indicatore di caricamento
            $('#selected-pages-container').append(
                '<div class="tag loading" data-id="' + tempId + '">' + tagText + '</div>'
            );

            // Qui puoi aggiungere la chiamata AJAX per creare effettivamente l'elemento
            // Per ora lo simuliamo
            fakeIds.push(tagText);

            // Se l'opzione non esiste già nel select, aggiungila
            if (!$('#pageIds').find("option[value='" + tempId + "']").length) {
                var newOption = new Option(tagText, tempId, true, true);
                $('#pageIds').append(newOption).trigger('change');
            }

            setTimeout(function () {
            updatePageIdsColor();
            }, 50);
        } else {
            // Gestione normale per elementi esistenti
            $('#selected-pages-container').append(
                '<div class="tag" data-id="' + data.id + '">' + data.text + '</div>'
            );
            setTimeout(function () {
            updatePageIdsColor();
            }, 50);
        }
    }).on('select2:unselect', function(e) {
        var data = e.params.data;
        if (data) {
            $('#selected-pages-container .tag[data-id="' + data.id + '"]').remove();
            setTimeout(function () {
            updatePageIdsColor();
            }, 50);
        }
    });
    
     // Imposta i valori iniziali
    $('#pageIds').val(selected).trigger('change');
    
    initAutocomplete();
    bindQrCodePrint();
    // Switch
    // -------------------------
    $(".switchery").each(function () {
        if ($(this).val() === 'true') {
            $(this).attr("checked", "checked");
        }
    });
    if (Array.prototype.forEach) {
        var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
        elems.forEach(function (html) {
            var switchery = new Switchery(html);
        });
    } else {
        var elems = document.querySelectorAll('.switchery');

        for (var i = 0; i < elems.length; i++) {
            var switchery = new Switchery(elems[i]);
        }
    }

    // Checkboxes/radios (Uniform)
    // ------------------------------

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });

    // Lunghezza campi
    // -------------------------
    $('.maxlength').maxlength({
        alwaysShow: true,
        placement: 'top-right'
    });

    // Select with search
    $('.select-search-standard').select2({
        matcher: matchCustom // per cercare velocemente le ore senza digitare i :
    }
    );

    // Select with search
    $('.select-search').select2({
        language: 'it',
        maximumSelectionLength: 1,
        containerCssClass: 'border-bottom-ddd text-333',
        tags: true
    });

    $('.summernote').summernote({
        callbacks: {
            onPaste: function(e) {
                var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                e.preventDefault();
                document.execCommand('insertText', false, bufferText);
            }
        },
        toolbar: [
            ['style', ['bold', 'italic']], // Grassetto e Italico
            ['fontsize', ['fontsize']], // Dimensione del testo
            ['para', ['ul', 'ol']], // Liste (puntate e numerate)
            ['insert', ['link']] // Inserimento di link
        ],
        height: 300, // Altezza dell'editor
        disableLinkTarget: false, // Permette di aprire link in una nuova finestra
        popover: { 
            image: [], link: [], air: [] // Disabilita i popover non necessari
        }
    });

    $('.daterange-single').daterangepicker({
        autoUpdateInput: true,
        locale: {
            format: 'DD/MM/YYYY',
            applyLabel: 'Applica',
            cancelLabel: 'Annulla',
            startLabel: 'Data inizio',
            endLabel: 'Data fine',
            customRangeLabel: 'Personalizzato',
            daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
            monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
            firstDay: 1
        },
        singleDatePicker: true
    });

    function matchCustom(params, data) {
        // If there are no search terms, return all of the data
        if ($.trim(params.term) === '') {
            return data;
        }

        // Do not display the item if there is no 'text' property
        if (typeof data.text === 'undefined') {
            return null;
        }

        // `params.term` should be the term that is used for searching
        // `data.text` is the text that is displayed for the data object
        if (data.text.replace(":", "").indexOf(params.term) > -1) {
            var modifiedData = $.extend({}, data, true);
            modifiedData.text;

            // You can return modified objects from here
            // This includes matching the `children` how you want in nested data sets
            return modifiedData;
        }

        // Return `null` if the term should not be displayed
        return null;
    }

    $('.select-search-multiple').select2({
        language: 'it',
        maximumSelectionLength: 5,
        containerCssClass: 'border-bottom-ddd text-333',
        tags: true
    });

    // Dropdown
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });

    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field, .note-editable', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Dropuploader
            else if (element.hasClass('drops')) {
                error.appendTo(element.parent().parent());
            }

            // Summernote
            else if (element.hasClass('summernote')) {
                error.appendTo(element.parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }

        },
        rules: {
            email: {
                email: true
            }
        }
    });

    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-primary',
                    action: function () {
                        window.location.href = $('#eventsUri').attr('href');
                    }
                }
            }
        });
    });

    $('#btn-save').click(function (event) {
        $('#page-form').attr('action', $('#eventSaveUri').attr('href'));
    });

    // bind delete action
    $('#btn-delete').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $('#eventRemoveUri').attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-trash-alt icon-2x"></i><br/><br/> Eliminazione evento',
            content: "Conferma eliminazione?",
            columnClass: "col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'btn-danger mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#eventsSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                    function (response, status, errorThrown) {
                                        $.unblockUI();
                                        // warn
                                        $.alert({
                                            theme: 'supervan',
                                            escapeKey: true,
                                            animation: 'top',
                                            closeAnimation: 'bottom',
                                            backgroundDismiss: true,
                                            title: 'Oh oh! :(',
                                            content: label('common.delete.failed')
                                        });
                                    }
                        });
                    }
                }
            }
        });

        return false;
    });

});

function updatePageIdsColor() {
    // coloro di rosso le fakePageId così per renderlo chiaro
    if (fakeIds.length > 0) {
        fakeIds.forEach(function (title) {
            var escapedTitle = CSS.escape(title);
            $(".select2-selection__choice[title='" + escapedTitle + "']").addClass("text-danger");
            $(".select2-selection__choice[title='" + escapedTitle + "']").find("span").addClass("text-danger");
        });
    }
}

// async registering autocomplete address
function initAutocomplete() {
    var input = $('#fullAddress')[0];
    var options = {
        offset: 2
    };
    autocomplete = new google.maps.places.Autocomplete(input, options);
    autocomplete.addListener('place_changed', selectAddress);

    // hacking google logo
    setTimeout(function () {
        $('.pac-container').removeClass('pac-logo');
    }, 1000);
}

function selectAddress() {
    (function () {
        // get city
        var city = '';
        var address = '';
        var number = '';
        var postalCode = '';
        var countryCode = '';
        var provinceCode = '';
        var venue = '';
        var place = autocomplete.getPlace();
        if (place !== null) {
            if (typeof place.address_components !== 'undefined') {
                for (var i = 0; i < place.address_components.length; i++) {
                    var type = place.address_components[i].types[0];
                    if (type === 'locality') {
                        city = place.address_components[i]['long_name'];
                    }
                    if (type === 'route') {
                        address = place.address_components[i]['long_name'];
                    }
                    if (type === 'street_number') {
                        number = place.address_components[i]['long_name'];
                    }
                    if (type === 'postal_code') {
                        postalCode = place.address_components[i]['long_name'];
                    }
                    if (type === 'administrative_area_level_2') {
                        provinceCode = place.address_components[i]['short_name'];
                    }
                    if (type === 'country') {
                        countryCode = place.address_components[i]['short_name'];
                    }
                    //                  type for venue
                    //                "point_of_interest"
                    //                "establishment"
                    if (place.types.includes("point_of_interest") || place.types.includes("establishment")) {
                        venue = place.name;
                    }
                }
                if (address !== '') {
                    if (number !== '') {
                        address += ' ' + number;
                    }
                }
            }
        }
        $('#city').val(city);
        $('#address').val(address);
        $('#postalCode').val(postalCode);
        $('#provinceCode').val(provinceCode).change();
        $('#countryCode').val(countryCode).change();
    })();
}

function bindStatusUpdate() {
    $('.status-update').off();
    $('.status-update').click(function (event) {

        var url = $(this).attr('href');

        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento stato!',
            content: "Confermi?",
            columnClass: "col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-primary mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: url,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        $.alert({
                                            theme: 'supervan',
                                            escapeKey: true,
                                            animation: 'top',
                                            closeAnimation: 'bottom',
                                            backgroundDismiss: true,
                                            title: 'Cambio stato! :)',
                                            content: 'Salvataggio riuscito.',
                                            buttons: {
                                                ok: {
                                                    text: 'OK',
                                                    btnClass: 'btn-default mb-5',
                                                    action: function () {
                                                        window.location.reload();
                                                    }
                                                },
                                            }
                                        });
                                    },
                            error:
                                    function (response, status, errorThrown) {
                                        $.unblockUI();
                                        // warn
                                        let msg = 'Aggiornamento non riuscito.';
                                        if (response && response.responseText) {
                                            msg = response.responseText.substring(0, 200);
                                        }
                                        $.alert({
                                            theme: 'supervan',
                                            escapeKey: true,
                                            animation: 'top',
                                            closeAnimation: 'bottom',
                                            backgroundDismiss: true,
                                            title: 'Oh oh! :(',
                                            content: msg + '<br/>'
                                        });
                                    }
                        });
                    }
                }
            }
        });

        return false;
    });
}

function bindQrCodePrint() {

    $('.qrcode-print').click(function (event) {
        // disable the default form submission
        event.preventDefault();

        var attr = $(this).attr('href');
        var postToUrl = $(this).attr('href');
        if (typeof attr !== typeof undefined && attr !== false) {
            postToUrl = $(this).attr('href');
        } else {
            postToUrl = $(this).parent().attr('href');
        }

        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: "Creazione qrcode",
            content: "Continuare?",
            columnClass: "col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-yellow',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        if (returndata) {
                                            $.unblockUI();
                                            var url = $("#imageUri").attr("href") + returndata.toString();
                                            window.open(url, '_blank');
                                            location.reload();
                                        }
                                    },
                            error:
                                    function (response, status, errorThrown) {
                                        $.unblockUI();
                                        // warn
                                        $.alert({
                                            theme: 'supervan',
                                            escapeKey: true,
                                            animation: 'top',
                                            closeAnimation: 'bottom',
                                            backgroundDismiss: true,
                                            title: 'Oh oh! :(',
                                            content: 'Creazione qrcode non riuscita.<br/>'
                                        });
                                    }
                        });
                    }
                }
            }
        });

        return false;
    });
}

function toCamelCase(str) {
    return str
            .split(' ') // Split the string into words
            .map((word, index) => {
                return word.charAt(0).toUpperCase() + word.slice(1); // Capitalize the first letter of subsequent words
            })
            .join(' '); // Join the words back into a single string
}