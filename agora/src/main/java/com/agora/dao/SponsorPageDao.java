package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.SponsorPage;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 * DAO for SponsorPage operations
 * 
 * <AUTHOR> Agent
 */
public class SponsorPageDao {

    public static SponsorPage loadSponsorPage(ObjectId sponsorPageId) throws Exception {
        if (sponsorPageId == null) {
            throw new InvalidParameterException("empty sponsorPageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_page");
        Document doc = collection.find(eq("_id", sponsorPageId)).first();
        return Manager.fromDocument(doc, SponsorPage.class);
    }

    public static List<SponsorPage> loadSponsorPageList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_page");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("sort"), ascending("creation")));
        return Manager.fromDocumentList(list, SponsorPage.class);
    }

    public static List<SponsorPage> loadActiveSponsorPageList() throws Exception {
        Date now = new Date();
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_page");
        FindIterable<Document> list = collection
                .find(new Document("cancelled", new Document("$ne", true))
                        .append("expirationDate", new Document("$gte", now)))
                .sort(orderBy(ascending("sort"), ascending("creation")));
        return Manager.fromDocumentList(list, SponsorPage.class);
    }

    public static ObjectId insertSponsorPage(SponsorPage sponsorPage) throws Exception {
        if (sponsorPage == null) {
            throw new InvalidParameterException("empty sponsorPage");
        }

        // defaults
        Date now = new Date();

        // internals
        sponsorPage.setCreation(now);
        sponsorPage.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_page");
        Document doc = Manager.toDocument(sponsorPage);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateSponsorPage(SponsorPage sponsorPage) throws Exception {
        if (sponsorPage == null) {
            throw new InvalidParameterException("empty sponsorPage");
        }

        // defaults
        Date now = new Date();

        // internals
        sponsorPage.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_page");
        collection.replaceOne(
                new Document("_id", sponsorPage.getId()),
                Manager.toDocument(sponsorPage)
        );
    }

    public static void removeSponsorPage(ObjectId sponsorPageId) throws Exception {
        if (sponsorPageId == null) {
            throw new InvalidParameterException("empty sponsorPageId");
        }

        SponsorPage sponsorPage = loadSponsorPage(sponsorPageId);
        if (sponsorPage != null) {
            sponsorPage.setCancelled(true);
            updateSponsorPage(sponsorPage);
        }
    }
}
