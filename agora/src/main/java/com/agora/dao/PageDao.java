package com.agora.dao;

import com.agora.commons.StorageCommons;
import com.agora.core.Manager;
import com.agora.pojo.Page;
import com.agora.pojo.User;
import com.agora.pojo.types.ImageType;
import com.agora.pojo.types.ResultSortType;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import com.github.slugify.Slugify;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

import static com.mongodb.MongoClient.getDefaultCodecRegistry;

import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;

import static com.mongodb.client.model.Accumulators.sum;

import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;

import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;

import static com.mongodb.client.model.Aggregates.*;
import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.security.InvalidParameterException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecProvider;

import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;

import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.ClassModel;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 * <AUTHOR>
 */
public class PageDao {

    public static Page loadPage(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        Document doc = collection.find(eq("_id", pageId)).first();
        return Manager.fromDocument(doc, Page.class);
    }

    // per ora usata solo per cache, quindi metto controllo su cancelled (la query parte da page_follower, quindi non riesco a controllare direttamente li)
    public static List<Page> loadPages(List<ObjectId> pageIds) throws Exception {
        if (pageIds == null) {
            throw new InvalidParameterException("empty pageIds");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        FindIterable<Document> list = collection.find(and(in("_id", pageIds), ne("cancelled", true)));
        return Manager.fromDocumentList(list, Page.class);
    }

    public static Page loadUserPage(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        Document doc = collection.find(and(ne("cancelled", true), eq("isUserPage", true), eq("ownerId", userId))).first();
        return Manager.fromDocument(doc, Page.class);
    }

    public static List<Page> loadPageList() throws Exception {
        return loadPageList(true);
    }

    public static List<Page> loadPageList(Boolean includeFake) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), ne("status", "draft"), or(eq("isFake", BooleanUtils.isTrue(includeFake) ? true : null), eq("isFake", BooleanUtils.isTrue(includeFake) ? null : "false"))))
                .sort(orderBy(descending("startDate")));
        return Manager.fromDocumentList(list, Page.class);
    }

    public static List<Page> loadPageListByName(String name) throws Exception {
        name = StringUtils.isNotBlank(name) ? name : "";
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), regex("name", Pattern.compile("^" + name, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE))))
                .sort(orderBy(ascending("name")))
                .limit(20);
        return Manager.fromDocumentList(list, Page.class);
    }

    public static List<Page> loadPageListByName(String name, ObjectId ownerId) throws Exception {
        return loadPageListByName(name, ownerId, true, 0, 0);
    }

    public static List<Page> loadPageListByName(String name, ObjectId ownerId, Boolean includeFake, int skip, int limit) throws Exception {
        name = StringUtils.isNotBlank(name) ? name : "";
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(or(eq("isFake", BooleanUtils.isTrue(includeFake) ? true : null), eq("isFake", BooleanUtils.isTrue(includeFake) ? null : "false")));
        filters.add(regex("name", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)));

        if (ownerId != null) {
            filters.add(or(eq("pageTagging", "everyone"), eq("ownerId", ownerId)));
        } else {
            filters.add(eq("pageTagging", "everyone"));
        }

        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(ascending("name")))
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, Page.class);
    }

    public static List<String> loadPageTagListBy(String name, int skip, int limit) throws Exception {
        if (name == null) {
            throw new InvalidParameterException("empty name");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");

        return collection.aggregate(List.of(
                        new Document("$unwind", "$tags"),
                        new Document("$match", new Document("tags", new Document("$regex", name).append("$options", "i"))),
                        new Document("$group", new Document("_id", "$tags")),
                        new Document("$replaceRoot", new Document("newRoot", new Document("tag", "$_id"))),
                        new Document("$skip", skip),
                        new Document("$limit", limit)
                )).map(doc -> doc.getString("tag"))
                .into(new ArrayList<>());

    }

    public static List<Page> loadPageListBy(String name, int skip, int limit) throws Exception {
        return loadPageListBy(name, skip, limit, true);
    }

    public static List<Page> loadPageListBy(String name, int skip, int limit, Boolean includeFake) throws Exception {
        name = StringUtils.isNotBlank(name) ? name : "";
        String nameTag = name;
        
        Slugify slg = new Slugify();
        name = slg.slugify(name);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(or(eq("isFake", BooleanUtils.isTrue(includeFake) ? true : null), eq("isFake", BooleanUtils.isTrue(includeFake) ? null : "false")));
//        filters.add(or(
//                regex("name", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
//                regex("tags", Pattern.compile("^" + Pattern.quote(name) + "$", Pattern.CASE_INSENSITIVE))
//        ));
        // 07/03/25: usiamo titleIdentifier dato che identifier può essere modificato
        filters.add(or(
                regex("titleIdentifier", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
                regex("tags", Pattern.compile("^" + Pattern.quote(nameTag) + "$", Pattern.CASE_INSENSITIVE))
        ));

        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(ascending("name"), descending("creation")))
                //                .projection(Projections.metaTextScore("score"))
                //                .sort(Sorts.metaTextScore("score"))
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, Page.class);
    }

    public static List<Page> loadCorrelatedPages(Page page, ObjectId userId, Integer quantity) throws Exception {
        List<String> tags = page.getTags();
        ObjectId pageId = page.getId();

        if (tags == null || tags.isEmpty()) {
            throw new InvalidParameterException("empty tags");
        }

        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        // Trova tutti gli eventi che contengono almeno due pageIds
        MongoCollection<Document> eventCollection = Manager.mongoDatabase.getCollection("event");

        // Cerchiamo eventi che includano il pageId specificato
        List<Bson> pipeline = Arrays.asList(
                match(Filters.in("pageIds", pageId)),
                unwind("$pageIds"),
                group("$pageIds", Accumulators.sum("count", 1)),
                match(Filters.gte("count", 2))
        );

        AggregateIterable<Document> matchingPages = eventCollection.aggregate(pipeline);
        Map<ObjectId, Integer> filteredPageIds = new HashMap<>();
        for (Document doc : matchingPages) {
            if (doc.containsKey("_id") && doc.containsKey("count")) {
                ObjectId tmpPageId = (ObjectId) doc.get("_id");
                int count = doc.getInteger("count");
                filteredPageIds.put(tmpPageId, count);
            }
        }

        // da qua bisogna togliere quelle che già seguo
        List<ObjectId> followedPage = PageFollowerDao.loadPageFollowedIdListByUser(userId);
        for (ObjectId tmpPageId : followedPage) {
            filteredPageIds.remove(tmpPageId);
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");

        // (!) LIMITATO A 50 PAGINE PER EVITARE TEMPI TROPPO LUNGHI (!)
        FindIterable<Document> list = collection.find(and(
                        ne("cancelled", true),
                        ne("status", "draft"),
                        ne("_id", pageId),
                        or(eq("isFake", null), eq("isFake", "false")),
                        /*in("tags", tags),*/
                        in("_id", filteredPageIds.keySet()) // nuovo filtro
                ))
                .sort(Sorts.descending("startDate"))
                .limit(quantity != null && quantity > 0 ? quantity : 50);

        List<Page> pages = Manager.fromDocumentList(list, Page.class);

        Map<ObjectId, Integer> sharedTagsCounter = new HashMap<>();
        for (Page tmpPage : pages) {
            if (tmpPage.getTags() != null && !tmpPage.getTags().isEmpty()) {
                for (String tag : tmpPage.getTags()) {
                    if (tags.contains(tag)) {
                        sharedTagsCounter.put(tmpPage.getId(), sharedTagsCounter.getOrDefault(tmpPage.getId(), 0) + 1);
                    }
                }
            }
        }

        // sort per numero di eventi in comune e poi per numero di tags in comune
        pages.sort((p1, p2) -> {
            int sharedEvents1 = filteredPageIds.getOrDefault(p1.getId(), 0);
            int sharedEvents2 = filteredPageIds.getOrDefault(p2.getId(), 0);
            if (sharedEvents1 != sharedEvents2) {
                return Integer.compare(sharedEvents2, sharedEvents1);
            }

            int sharedTags1 = sharedTagsCounter.getOrDefault(p1.getId(), 0);
            int sharedTags2 = sharedTagsCounter.getOrDefault(p2.getId(), 0);
            if (sharedTags1 != sharedTags2) {
                return Integer.compare(sharedTags2, sharedTags1);
            }

            return p1.getId().compareTo(p2.getId());
        });

        return pages;
    }

    public static List<Page> loadPageListForCache(int minFollowers) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");

        List<Document> pipeline = Arrays.asList(
                // Group by pageId and count followers
                new Document("$group", new Document("_id", "$pageId")
                        .append("followerCount", new Document("$sum", 1))),
                // Only keep pages with at least minFollowers followers
                new Document("$match", new Document("followerCount", new Document("$gte", minFollowers))),
                // Project only the pageId (_id)
                new Document("$project", new Document("_id", 0)
                        .append("pageId", "$_id"))
        );

        AggregateIterable<Document> result = collection.aggregate(pipeline);

        List<ObjectId> pageIds = new ArrayList<>();
        for (Document doc : result) {
            pageIds.add(doc.getObjectId("pageId"));
        }

        // bisogna caricare anche le pagine che hanno il numero di followes fake
        collection = Manager.mongoDatabase.getCollection("page");
        FindIterable<Document> list = collection.find(and(
                ne("cancelled", true),
                ne("isFake", true),
                ne("followers", null),
                gte("followers", minFollowers)
        ));
        List<Page> validPages = Manager.fromDocumentList(list, Page.class);
        for (Page page : validPages) {
            if (!pageIds.contains(page.getId())) {
                pageIds.add(page.getId());
            }
        }

        if (!pageIds.isEmpty()) {
            return loadPages(pageIds);
        } else {
            return null;
        }
    }

    public static List<Page> loadWhoPeople(String provinceCode, ObjectId userId, Integer quantity, String sort) throws Exception, IllegalAccessException, InvocationTargetException {
        return loadWhoPeople(provinceCode, userId, quantity, sort, true);
    }

    public static List<Page> loadWhoPeople(String provinceCode, ObjectId userId, Integer quantity, String sort, Boolean includeFake) throws Exception, IllegalAccessException, InvocationTargetException {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");

        // Esecuzione della query di aggregazione
        List<Page> result = new ArrayList<>();

        if (userId == null) {
            // Costruisci il pipeline di aggregazione
            List<Document> pipeline = Arrays.asList(
                    new Document("$match", new Document("isUserPage", true).append("cancelled", new Document("$ne", true))),
                    new Document("$sample", new Document("size", 20))
            );

            // Esegui la query di aggregazione
            collection.aggregate(pipeline).map(document -> {
                Page page = new Page();
                try {
                    BeanUtils.populate(page, document);
                    if (document.containsKey("_id")) {
                        page.setId((ObjectId) document.get("_id"));
                    }
                } catch (IllegalAccessException | InvocationTargetException ex) {
                    Logger.getLogger(PageDao.class.getName()).log(Level.SEVERE, null, ex);
                }
                return page;
            }).into(result);

        } else {
            Bson sortBson = Sorts.descending("creation");
            if (sort != null) {
                if (StringUtils.equalsIgnoreCase(sort, ResultSortType.creationAsc.toString())) {
                    sortBson = Sorts.ascending("creation");
                }
                if (StringUtils.equalsIgnoreCase(sort, ResultSortType.creationDesc.toString())) {
                    sortBson = Sorts.descending("creation");
                }
                if (StringUtils.equalsIgnoreCase(sort, ResultSortType.lastUpdateAsc.toString())) {
                    sortBson = Sorts.ascending("lastUpdate");
                }
                if (StringUtils.equalsIgnoreCase(sort, ResultSortType.lastUpdateDesc.toString())) {
                    sortBson = Sorts.descending("lastUpdate");
                }
            }

            List<Bson> pipeline = new ArrayList<>();

            // Filtro per il codice della provincia
            if (provinceCode != null) {
                pipeline.add(Aggregates.match(Filters.eq("provinceCode", provinceCode)));
            }

            // Aggiunta di altri filtri alla pipeline
            pipeline.add(Aggregates.match(Filters.and(
                    Filters.ne("cancelled", true),
                    Filters.eq("isUserPage", true),
                    Filters.ne("ownerId", userId),
                    Filters.and(or(eq("isFake", BooleanUtils.isTrue(includeFake) ? true : null), eq("isFake", BooleanUtils.isTrue(includeFake) ? null : "false"))))));

            pipeline.add(Aggregates.lookup("page_follower", "_id", "pageId", "pageFollowers"));
            pipeline.add(Aggregates.match(Filters.and(
                    Filters.ne("cancelled", true),
                    Filters.or(
                            Filters.eq("pageFollowers", null),
                            Filters.eq("pageFollowers", new ArrayList<>()),
                            Filters.not(Filters.elemMatch("pageFollowers", Filters.eq("userId", userId)))
                    )
            )));

            // Limite sulla quantità se specificato
            if (quantity != null && quantity > 0) {
                pipeline.add(Aggregates.limit(quantity));
            }

            // Aggiunta dell'ordinamento
            pipeline.add(Aggregates.sort(sortBson));

            collection.aggregate(pipeline).map(document -> {
                Page page = new Page();
                try {
                    BeanUtils.populate(page, document);
                    if (document.containsKey("_id")) {
                        page.setId((ObjectId) document.get("_id"));
                    }
                } catch (IllegalAccessException | InvocationTargetException ex) {
                    Logger.getLogger(PageDao.class.getName()).log(Level.SEVERE, null, ex);
                }
                return page;
            }).into(result);

            // Mischia i risultati se richiesto un ordinamento casuale
            if (Objects.equals(sort, ResultSortType.random.toString())) {
                Collections.shuffle(result);
            }
        }

        return result;
    }

    public static List<Page> loadWhoPage(String provinceCode, ObjectId userId, Integer quantity, String sort) throws Exception, IllegalAccessException, InvocationTargetException {
        return loadWhoPage(provinceCode, userId, quantity, sort, true);
    }

    public static List<Page> loadWhoPage(String provinceCode, ObjectId userId, Integer quantity, String sort, Boolean includeFake) throws Exception, IllegalAccessException, InvocationTargetException {
        ClassModel<Page> colorModel = ClassModel.builder(Page.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(colorModel).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Page> collection = Manager.mongoDatabase.getCollection("page", Page.class).withCodecRegistry(pojoCodecRegistry);
        // MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");

        // Esecuzione della query di aggregazione
        List<Page> result = new ArrayList<>();

        if (userId == null) {
            // Costruisci il pipeline di aggregazione per estrarre 5 pagine casuali con isUserPage=true
            List<Document> pipeline = Arrays.asList(
                    new Document("$match", new Document("isUserPage", new Document("$ne", true)).append("cancelled", new Document("$ne", true))),
                    new Document("$sample", new Document("size", quantity))
            );

            // Esegui la query di aggregazione
//            collection.aggregate(pipeline).map(document -> {
//                Page page = new Page();
//                try {
//                    BeanUtils.populate(page, document);
//                    if (document.containsKey("_id")) {
//                        page.setId((ObjectId) document.get("_id"));
//                    }
//                } catch (IllegalAccessException | InvocationTargetException ex) {
//                    Logger.getLogger(PageDao.class.getName()).log(Level.SEVERE, null, ex);
//                }
//                return page;
//            }).into(result);
            collection.aggregate(pipeline).into(result);
        } else {
            Bson sortBson = null;
            List<Bson> pipeline = new ArrayList<>();
            if (StringUtils.equalsIgnoreCase(sort, ResultSortType.random.toString())) {
                pipeline.add(Aggregates.match(
                        Filters.and(
                                Filters.ne("cancelled", true),
                                Filters.ne("isUserPage", true),
                                Filters.ne("ownerId", userId),
                                Filters.nin("pageId", PageFollowerDao.loadPageFollowedIdListByUser(userId)),
                                Filters.and(or(eq("isFake", BooleanUtils.isTrue(includeFake) ? true : null), eq("isFake", BooleanUtils.isTrue(includeFake) ? null : "false")))
                        )
                ));
//                pipeline.add(Aggregates.lookup("page_follower", "_id", "pageId", "pageFollowers"));
//                pipeline.add(Aggregates.match(
//                        Filters.and(
//                                Filters.ne("cancelled", true),
//                                Filters.or(
//                                        Filters.eq("pageFollowers", null),
//                                        Filters.eq("pageFollowers", Collections.emptyList()),
//                                        Filters.not(Filters.elemMatch("pageFollowers", Filters.eq("userId", userId)))
//                                )
//                        )
//                ));
                pipeline.add(Aggregates.sample(quantity));
            } else {
                if (StringUtils.equalsIgnoreCase(sort, ResultSortType.creationAsc.toString())) {
                    sortBson = Sorts.ascending("creation");
                } else if (StringUtils.equalsIgnoreCase(sort, ResultSortType.creationDesc.toString())) {
                    sortBson = Sorts.descending("creation");
                } else if (StringUtils.equalsIgnoreCase(sort, ResultSortType.lastUpdateAsc.toString())) {
                    sortBson = Sorts.ascending("lastUpdate");
                } else if (StringUtils.equalsIgnoreCase(sort, ResultSortType.lastUpdateDesc.toString())) {
                    sortBson = Sorts.descending("lastUpdate");
                }

                // Filtro per il codice della provincia
                if (provinceCode != null) {
                    pipeline.add(Aggregates.match(Filters.eq("provinceCode", provinceCode)));
                }

                // Aggiunta di altri filtri alla pipeline
                pipeline.add(Aggregates.match(
                        Filters.and(
                                Filters.ne("cancelled", true),
                                Filters.ne("isUserPage", true),
                                Filters.ne("ownerId", userId),
                                Filters.nin("pageId", PageFollowerDao.loadPageFollowedIdListByUser(userId)),
                                Filters.and(or(eq("isFake", BooleanUtils.isTrue(includeFake) ? true : null), eq("isFake", BooleanUtils.isTrue(includeFake) ? null : "false")))
                        )
                ));
//                pipeline.add(Aggregates.lookup("page_follower", "_id", "pageId", "pageFollowers"));
//                pipeline.add(Aggregates.match(
//                        Filters.and(
//                                Filters.ne("cancelled", true),
//                                Filters.or(
//                                        Filters.eq("pageFollowers", null),
//                                        Filters.eq("pageFollowers", Collections.emptyList()),
//                                        Filters.not(Filters.elemMatch("pageFollowers", Filters.eq("userId", userId)))
//                                )
//                        )
//                ));

                // Limite sulla quantità se specificato
                if (quantity != null && quantity > 0) {
                    pipeline.add(Aggregates.limit(quantity));
                }

                // Aggiunta dell'ordinamento
                if (sortBson != null) {
                    pipeline.add(Aggregates.sort(sortBson));
                }
            }

//            collection.aggregate(pipeline).map(document -> {
//                Page page = new Page();
//                try {
//                    BeanUtils.populate(page, document);
//                    if (document.containsKey("_id")) {
//                        page.setId((ObjectId) document.get("_id"));
//                    }
//                } catch (IllegalAccessException | InvocationTargetException ex) {
//                    Logger.getLogger(PageDao.class.getName()).log(Level.SEVERE, null, ex);
//                }
//                return page;
//            }).into(result);
            collection.aggregate(pipeline).into(result);

            // Mischia i risultati se richiesto un ordinamento casuale
            if (Objects.equals(sort, ResultSortType.random.toString())) {
                Collections.shuffle(result);
            }
        }

        return result;
    }

    public static List<Page> loadPageListByOwnerId(ObjectId userId) throws Exception {
        return loadPageListByOwnerId(userId, 0, 0);
    }

    public static List<Page> loadPageListByOwnerId(ObjectId userId, int skip, int limit) throws Exception {
        return loadPageListByOwnerId(userId, skip, limit, true);
    }

    public static List<Page> loadPageListByOwnerId(ObjectId userId, int skip, int limit, Boolean includeFake) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), ne("status", "draft"), eq("ownerId", userId), or(eq("isFake", BooleanUtils.isTrue(includeFake) ? true : null), eq("isFake", BooleanUtils.isTrue(includeFake) ? null : "false"))))
                .sort(orderBy(descending("name")))
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, Page.class);
    }

    public static long loadPageCountByLanguage(String language) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        long count = collection.count(
                and(
                        eq("language", language),
                        ne("cancelled", true))
        );

        return count;
    }

    public static long loadPageCountBy(String name) throws Exception {
        return loadPageCountBy(name, null, false);
    }

    public static long loadPageCountBy(String name, ObjectId ownerId, Boolean pageTagging) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");

        name = StringUtils.isNotBlank(name) ? name : "";

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(regex("name", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)));

        if (BooleanUtils.isTrue(pageTagging)) {
            if (ownerId != null) {
                filters.add(or(eq("pageTagging", "everyone"), eq("ownerId", ownerId)));
            } else {
                filters.add(eq("pageTagging", "everyone"));
            }
        }

        return collection.count(and(filters));

    }

    public static long loadPageCountByDate(Date date) throws Exception {
        if (date == null) {
            throw new InvalidParameterException("empty date");
        }

        Date from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(date));
        Date to = MongoUtils.toComparableDate(TimeUtils.endOfDay(date));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        gte("creation", from),
                        lte("creation", to))
        );

        return count;
    }

    public static Map<String, Integer> loadPageCountByDate() {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");

        DBObject yearMonthFormat = new BasicDBObject("format", "%Y-%m");
        yearMonthFormat.put("date", "$creation");
        DBObject yearMonth = new BasicDBObject("$dateToString", yearMonthFormat);

        AggregateIterable<Document> newbies = collection
                .aggregate(Arrays.asList(
                        match(and(
                                ne("cancelled", true),
                                gte("creation", TimeUtils.beginOfYear(TimeUtils.today())),
                                lte("creation", TimeUtils.endOfYear(TimeUtils.today()))
                        )),
                        group(yearMonth, sum("count", 1)),
                        sort(orderBy(ascending("_id")))
                ));

        Map<String, Integer> values = new HashMap<>();
        for (Document newbie : newbies) {
            values.put(newbie.getString("_id"), newbie.getInteger("count", 0));
        }

        return values;
    }

    public static List<BestPerformer> loadBestPerformerByCustomer() {
        return loadBestPerformerByCustomer(true);
    }

    public static List<BestPerformer> loadBestPerformerByCustomer(Boolean includeFake) {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));
        filters.add(or(eq("isFake", BooleanUtils.isTrue(includeFake) ? true : null), eq("isFake", BooleanUtils.isTrue(includeFake) ? null : "false")));

        AggregateIterable<Document> pages = collection
                .aggregate(Arrays.asList(
                        match(and(filters)),
                        group("$userId", sum("count", 1)),
                        sort(orderBy(descending("count"))),
                        limit(10)
                ));

        List<BestPerformer> list = new ArrayList<>();
        for (Document order : pages) {

            BestPerformer performer = new BestPerformer();
            performer.setId(order.getObjectId("_id"));
            performer.setCount(order.getInteger("count"));
            list.add(performer);

        }
        return list;

    }

    public enum BestPerformerValueType {
        count
    }

    public static class BestPerformer {

        private ObjectId id;
        private Integer count;

        public ObjectId getId() {
            return id;
        }

        public void setId(ObjectId id) {
            this.id = id;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }

    }

    public static List<Page> loadPageListByDateRange(Date from, Date to) throws Exception {
        return loadPageListByDateRange(from, to, 0, 0);
    }

    public static List<Page> loadPageListByDateRange(Date from, Date to, int skip, int limit) throws Exception {
        return loadPageListByDateRange(from, to, skip, limit, true);
    }

    public static List<Page> loadPageListByDateRange(Date from, Date to, int skip, int limit, Boolean includeFake) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }

        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        FindIterable<Document> list = collection.find(and(
                        ne("cancelled", true),
                        and(or(eq("isFake", BooleanUtils.isTrue(includeFake) ? true : null), eq("isFake", BooleanUtils.isTrue(includeFake) ? null : "false"))),
                        gte("creation", from),
                        lte("creation", to))
                )
                .skip(skip > 0 ? skip : 0)
                .limit(limit > 0 ? limit : 0);

        return Manager.fromDocumentList(list, Page.class);
    }

    public static Page loadPageByIdentifier(String identifier) throws Exception {
        if (identifier == null) {
            throw new InvalidParameterException("empty identifier");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        Document doc = collection.find(and(eq("identifier", identifier), ne("cancelled", true))).first();
        return Manager.fromDocument(doc, Page.class);
    }

    public static Page loadPageByQrCode(String qrcode) throws Exception {
        if (qrcode == null) {
            throw new InvalidParameterException("empty qrcode");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        Document doc = collection.find(and(eq("qrcode", qrcode), ne("cancelled", true))).first();
        return Manager.fromDocument(doc, Page.class);
    }

    public static List<Page> loadDuplicatePages(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        ClassModel<Page> pageModel = ClassModel.builder(Page.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(pageModel).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));
        MongoCollection<Page> collection = Manager.mongoDatabase.getCollection("page", Page.class).withCodecRegistry(pojoCodecRegistry);

        // Aggregation pipeline per trovare pagine con lo stesso nome e stesso ownerId
        List<Document> pipeline = Arrays.asList(
                new Document("$match", new Document("ownerId", userId)
                        .append("cancelled", new Document("$ne", true))),
                new Document("$group", new Document("_id", "$name")
                        .append("count", new Document("$sum", 1))
                        .append("pages", new Document("$push", "$$ROOT"))),
                new Document("$match", new Document("count", new Document("$gt", 1))),
                new Document("$unwind", "$pages"),
                new Document("$replaceRoot", new Document("newRoot", "$pages")),
                new Document("$sort", new Document("creation", -1)) // Ordina per creation in ordine decrescente
        );

        // Esegui l'aggregazione e converti i risultati in una lista di oggetti Page
        List<Page> result = new ArrayList<>();
        collection.aggregate(pipeline).into(result);
        return result;
    }

    public static List<Page> loadHomePages(List<ObjectId> excludedIds) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("wall_page_ranking");
        FindIterable<Document> list;
        if (excludedIds != null) {
            list = collection
                    .find(nin("_id", excludedIds))
                    .sort(orderBy(descending("ranking"), descending("_id")))
                    .limit(9);
        } else {
            list = collection
                    .find()
                    .sort(orderBy(descending("ranking"), descending("_id")))
                    .limit(9);
        }

        List<ObjectId> pageIds = new ArrayList<>();
        for (Document doc : list) {
            pageIds.add(doc.getObjectId("_id"));
        }
        if (!pageIds.isEmpty()) {
            return loadPages(pageIds);
        } else {
            return null;
        }
    }

    public static ObjectId insertPage(Page page) throws Exception {
        if (page == null) {
            throw new InvalidParameterException("empty page");
        }

        // defaults
        Date now = new Date();

        // internals
        page.setCreation(now);
        page.setLastUpdate(now);

        // per evitare di mettere un "or" nelle query metto a null il campo
        // così basta controllare per i valori "true" o "null"
        if (BooleanUtils.isFalse(page.getIsFake())) {
            page.setIsFake(null);
        }

        // aggiornamento titleIdentifier usato nella ricerca
        if (StringUtils.isNotBlank(page.getName())) {
            Slugify slg = new Slugify();
            page.setTitleIdentifier(slg.slugify(page.getName()));
        } else {
            page.setTitleIdentifier(null); // forse non sarebbe neanche da fare ?
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        Document doc = Manager.toDocument(page);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static ObjectId insertWallPage(ObjectId pageId, Double ranking) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        if (ranking == null) {
            throw new InvalidParameterException("empty ranking");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("wall_page_ranking");
        Document doc = new Document("_id", pageId);
        doc.append("ranking", ranking);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updatePage(Page page) throws Exception {
        if (page == null) {
            throw new InvalidParameterException("empty page");
        }

        // defaults
        Date now = new Date();

        // internals
        page.setLastUpdate(now);

        // per evitare di mettere un "or" nelle query metto a null il campo
        // così basta controllare per i valori "true" o "null"
        if (BooleanUtils.isFalse(page.getIsFake())) {
            page.setIsFake(null);
        }

        // aggiornamento titleIdentifier usato nella ricerca
        if (StringUtils.isNotBlank(page.getName())) {
            Slugify slg = new Slugify();
            page.setTitleIdentifier(slg.slugify(page.getName()));
        } else {
            page.setTitleIdentifier(null); // forse non sarebbe neanche da fare ?
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        collection.replaceOne(
                new Document("_id", page.getId()),
                Manager.toDocument(page)
        );
    }

    public static void updatePageCancelled(ObjectId pageId, boolean cancelled) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        // defaults
        Date now = new Date();

        // update
        Page page = loadPage(pageId);
        page.setCancelled(cancelled);

        // internals
        page.setLastUpdate(now);

        // per evitare di mettere un "or" nelle query metto a null il campo
        // così basta controllare per i valori "true" o "null"
        if (BooleanUtils.isFalse(page.getIsFake())) {
            page.setIsFake(null);
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        collection.replaceOne(
                new Document("_id", page.getId()),
                Manager.toDocument(page)
        );

    }

    public static void deletePage(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        collection.deleteOne(
                new Document("_id", pageId)
        );
    }

    public static void updateProfileImage(String username, ObjectId pageId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }

        // defaults
        Date now = new Date();

        String originalFilename = image.getInput().getName();
        if (StringUtils.startsWith(originalFilename, "imagesystem")) {
            if (image.getMeta() != null && image.getMeta() != null) {
                Object metaObject = image.getMeta();

                if (metaObject instanceof LinkedHashMap) {
                    LinkedHashMap<String, Object> metaMap = (LinkedHashMap<String, Object>) metaObject;

                    // Ora puoi accedere ai dati nella mappa
                    Object valueFilename = metaMap.get("originalfilename");
                    // Esegui il cast se necessario
                    if (valueFilename instanceof String) {
                        originalFilename = (String) valueFilename;
                    }
                }
            }
        }

        // defaults
        String imageName = ImageDao.composeFilenameV2(ImageType.page, FilenameUtils.getBaseName(originalFilename), image.getExtension());
        String type = image.getType();

        // save image
        File savedFile = new File(StorageCommons.composePath(StorageCommons.StorageType.img, now, imageName));
        savedFile.getParentFile().mkdirs();
        try (OutputStream out = new FileOutputStream(savedFile)) {
            out.write(image.getBytes());
        }

        Integer width = null, height = null;
        if (image.getInput() != null) {
            width = image.getInput().getWidth();
            height = image.getInput().getHeight();
        }

        // save image
        ObjectId imageId = ImageDao.insertImageV2(savedFile,
                originalFilename,
                type,
                width,
                height
        );

        // update imageId
        Page page = loadPage(pageId);
        ObjectId oldImageId = page.getProfileImageId();
        page.setProfileImageId(imageId);

        // internals
        page.setLastUpdate(now);

        // per evitare di mettere un "or" nelle query metto a null il campo
        // così basta controllare per i valori "true" o "null"
        if (BooleanUtils.isFalse(page.getIsFake())) {
            page.setIsFake(null);
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        collection.replaceOne(
                new Document("_id", page.getId()),
                Manager.toDocument(page)
        );

        if (oldImageId != null) {
            ImageDao.deleteImage(oldImageId);
        }

    }

    public static void updateCoverImage(String username, ObjectId pageId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }

        // defaults
        Date now = new Date();

        // defaults
        String originalFilename = image.getInput().getName();
        if (StringUtils.startsWith(originalFilename, "imagesystem")) {
            if (image.getMeta() != null && image.getMeta() != null) {
                Object metaObject = image.getMeta();

                if (metaObject instanceof LinkedHashMap) {
                    LinkedHashMap<String, Object> metaMap = (LinkedHashMap<String, Object>) metaObject;

                    // Ora puoi accedere ai dati nella mappa
                    Object valueFilename = metaMap.get("originalfilename");
                    // Esegui il cast se necessario
                    if (valueFilename instanceof String) {
                        originalFilename = (String) valueFilename;
                    }
                }
            }
        }

        String imageName = ImageDao.composeFilenameV2(ImageType.pageCover, FilenameUtils.getBaseName(originalFilename), image.getExtension());
        String type = image.getType();

        // save image
        File savedFile = new File(StorageCommons.composePath(StorageCommons.StorageType.img, now, imageName));
        savedFile.getParentFile().mkdirs();
        try (OutputStream out = new FileOutputStream(savedFile)) {
            out.write(image.getBytes());
        }

        Integer width = null, height = null;
        if (image.getInput() != null) {
            width = image.getInput().getWidth();
            height = image.getInput().getHeight();
        }

        // save image
        ObjectId imageId = ImageDao.insertImageV2(savedFile,
                originalFilename,
                type,
                width,
                height
        );

        // update imageId
        Page page = loadPage(pageId);
        ObjectId oldImageId = page.getCoverImageId();
        page.setCoverImageId(imageId);

        // internals
        page.setLastUpdate(now);

        // per evitare di mettere un "or" nelle query metto a null il campo
        // così basta controllare per i valori "true" o "null"
        if (BooleanUtils.isFalse(page.getIsFake())) {
            page.setIsFake(null);
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        collection.replaceOne(
                new Document("_id", page.getId()),
                Manager.toDocument(page)
        );

        if (oldImageId != null) {
            ImageDao.deleteImage(oldImageId);
        }
    }

    public static void removeProfileImage(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        // defaults
        Date now = new Date();

        // update
        Page page = loadPage(pageId);
        ObjectId oldImageId = page.getProfileImageId();
        page.setProfileImageId(null);

        // internals
        page.setLastUpdate(now);

        // per evitare di mettere un "or" nelle query metto a null il campo
        // così basta controllare per i valori "true" o "null"
        if (BooleanUtils.isFalse(page.getIsFake())) {
            page.setIsFake(null);
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        collection.replaceOne(
                new Document("_id", page.getId()),
                Manager.toDocument(page)
        );

        if (oldImageId != null) {
            ImageDao.deleteImage(oldImageId);
        }

    }

    public static void removeCoverImage(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        // defaults
        Date now = new Date();

        // update
        Page page = loadPage(pageId);
        ObjectId oldImageId = page.getCoverImageId();
        page.setCoverImageId(null);

        // internals
        page.setLastUpdate(now);

        // per evitare di mettere un "or" nelle query metto a null il campo
        // così basta controllare per i valori "true" o "null"
        if (BooleanUtils.isFalse(page.getIsFake())) {
            page.setIsFake(null);
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        collection.replaceOne(
                new Document("_id", page.getId()),
                Manager.toDocument(page)
        );

        if (oldImageId != null) {
            ImageDao.deleteImage(oldImageId);
        }
    }

    public static ObjectId updateQrcodeImage(String filename, ObjectId pageId, String qrCodeSvg) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        if (qrCodeSvg == null) {
            throw new InvalidParameterException("empty image");
        }

        // defaults
        Date now = new Date();

        // defaults
        String imageName = ImageDao.composeFilenameV2(ImageType.qrcode, filename, "svg");
        String type = "image/svg+xml";

        // save image
        File savedFile = new File(StorageCommons.composePath(StorageCommons.StorageType.qrc, now, imageName));
        savedFile.getParentFile().mkdirs();

        try (FileWriter fileWriter = new FileWriter(savedFile)) {
            fileWriter.write(qrCodeSvg);
        } catch (IOException e) {
            // Gestisci l'eccezione se si verifica un errore durante la scrittura del file
            e.printStackTrace();
        }

        // save image
        ObjectId imageId = ImageDao.insertImageV2(savedFile,
                filename,
                type,
                null,
                null
        );

        // update imageId
        Page page = loadPage(pageId);
        ObjectId oldQrcodeId = page.getQrcodeFileId();
        page.setQrcode(filename);
        page.setQrcodeFileId(imageId);

        // internals
        page.setLastUpdate(now);

        // per evitare di mettere un "or" nelle query metto a null il campo
        // così basta controllare per i valori "true" o "null"
        if (BooleanUtils.isFalse(page.getIsFake())) {
            page.setIsFake(null);
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page");
        collection.replaceOne(
                new Document("_id", page.getId()),
                Manager.toDocument(page)
        );

        if (oldQrcodeId != null) {
            ImageDao.deleteImage(oldQrcodeId);
        }
        return imageId;
    }
}
