{% extends "be/include/base.html" %}
{% set pageActive = 'events' %}
{% block extrahead %}
{% if event.id is not empty %}
<title>Modifica evento {{ event.title | upper }}</title>
{% else %}
<title>Nuovo evento</title>
{% endif %}
<!-- Theme Custom CSS files -->
<link href="https://www.siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<link href="https://www.siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">
<link href="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">

<!-- Theme JS files -->
<!--
need the following activation, on google cloud console, to work:
* Google Places API Web Service
* Google Maps Javascript API

-->
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&libraries=places&language=it-IT"></script>

<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/anytime.min.js"></script>

<script src="https://www.siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/l10n/it.js"></script>
<script src="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/summernote/lang/summernote-it-IT.js"></script>
<!-- /theme JS files -->
<script src="{{ contextPath }}/be/js/pages/event-edit.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/be/js/lang/it-IT.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}
<a id="imageUri" style="display: none;" href="{{ paths('IMAGE_SYSTEM') }}?oid=" rel="nofollow"></a>

<a id="eventsUri" style="display: none" href="{{ paths('BE_EVENTS') }}"></a>
<a id="eventsSuccessUri" style="display: none;" href="{{ paths('BE_EVENTS') }}/ok" rel="nofollow"></a>
<a id="eventRemoveUri" style="display: none;" href="{{ paths('BE_EVENT_REMOVE') }}?eventId={{ event.id }}" rel="nofollow"></a>
<a id="dataPagesUri" style="display: none" href="{{ paths('DATA_PAGES') }}?ownerId={{ user.id }}"></a>

<!-- EVENT EDIT -->
{% if event.id is not empty %}
{% set saveUri = paths('BE_EVENT_EDIT_SAVE') + '?oid=' + event.id %}
{% else %}
{% set saveUri = paths('BE_EVENT_EDIT_SAVE') %}
{% endif %}
<a id="eventSaveUri" style="display: none" href="{{ saveUri }}"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-success">
                    <a href="{{ paths('EVENT_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-danger">
                    <a href="{{ paths('EVENT_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- EVENT EDIT -->
        {% if event.id is not empty %}
        {% set saveUri = paths('BE_EVENT_EDIT_SAVE') + '?oid=' + event.id %}
        {% else %}
        {% set saveUri = paths('BE_EVENT_EDIT_SAVE') %}
        {% endif %}
        <div class="row">
            <div class="col-lg-8 col-lg-offset-2">
                <!-- Basic layout-->
                <form id="event-form" class="form-horizontal" method="post" action="{{ saveUri }}" autocomplete="off">
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            {% if event.id is not empty %}
                            <h5 class="panel-title text-bold">Modifica evento {{ event.title | upper }}</h5>
                            <div class="heading-elements visible-elements">
                                <div class="btn-group heading-btn">
                                    <a href="{{ paths('BE_EVENT_REMOVE_POSTER') }}?oid={{ event.id }}" class="btn heading-btn btn-danger legitRipple"><i class="icon-trash-alt position-left"></i>ELIMINA COPERTINA</a>
                                    <a href="{{ paths('BE_EVENT_QRCODE_GENERATE') }}?oid={{ event.id }}" class="btn heading-btn btn-primary legitRipple qrcode-print"><i class="icon-file-pdf position-left"></i>QRCODE</a>
                                </div>
                            </div>
                            {% else %}
                            <h5 class="panel-title text-bold">Nuova evento</h5>
                            {% endif %}
                        </div>

                        <div class="panel-body">
                            <div class="form-group">                            
                                <!-- Cover image -->
                                <div class="col-lg-12">                                    
                                    <label class="form-label">Foto di copertina</label>
                                    <div class="slim rounded"
                                         data-max-file-size="5"
                                         data-save-initial-image="{{ event.coverImageId is not empty ? 'true' : 'false'}}"
                                         data-push="false"
                                         data-post="output"
                                         data-label="Carica un'immagine"
                                         data-label-loading=" "
                                         data-ratio="free"
                                         data-jpeg-compression=100
                                         data-button-edit-label="Modifica"
                                         data-button-remove-label="Elimina"
                                         data-button-download-label="Scarica"
                                         data-button-upload-label="Carica"
                                         data-button-rotate-label="Ruota"
                                         data-button-cancel-label="Cancella"
                                         data-button-confirm-label="Conferma"
                                         data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                         data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                         data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                         data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                         data-status-content-length="Il server non supporta file così grandi"
                                         data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                         data-status-upload-success="Immagine salvata">
                                        {% if event.coverImageId is not empty %}
                                        <img src="{{ paths('IMAGE_SYSTEM') }}?oid={{ event.coverImageId }}" alt=""/>
                                        {% endif %}
                                        <input type="file" id="cropper" name="uploaded-cover" data-show-caption="false" data-show-remove="true">
                                    </div>                                    
                                </div>
                                <!-- End Media -->                                                            
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Locandina:</label>
                                <div class="col-lg-9">
                                    <div id="uploader-text" style="display: none;">Trascina qui il file</div>                                                
                                    <div class="text-center">
                                        <input type="file" name="locandina" data-maxfilessize="4194304" attachment="true" >
                                    </div>
                                    {% if event.locandina is not empty %}
                                    <div class="panel panel-flat">
                                        <label class="col-lg-3 control-label">Locandina Attuale:</label>

                                        <div class="panel-body">
                                            <div class="content-group-xs" id="bullets"></div>
                                            {% set fileDecoded = get('DocumentDescriptor', event.locandina) %}
                                            <div class="media-body">
                                                <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ event.locandina }}" target="_blank" rel="noopener">
                                                    {{ fileDecoded.metadata.originalFilename }}
                                                </a>
                                                <button type="button" class="btn ml-10 btn-primary mb-0 w-25 delete-file" file-idx="{{ loop.index }}" filegroup="locandina">Rimuovi</button>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Categoria Evento:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select-search-standard form-control" id="category" name="category">
                                            <option value="">-</option>
                                            <option value="cultura" {{ event.category == 'cultura' ? 'selected' : '' }}>Cultura</option>
                                            <option value="sport" {{ event.category == 'sport' ? 'selected' : '' }}>Sport</option>
                                            <option value="intrattenimento" {{ event.category == 'intrattenimento' ? 'selected' : '' }}>Intrattenimento</option>
                                            <option value="cibo" {{ event.category == 'cibo' ? 'selected' : '' }}>Food & Drink - Enogastronomia</option>
                                            <option value="benessere" {{ event.category == 'benessere' ? 'selected' : '' }}>Benessere</option>
                                            <option value="business" {{ event.category == 'business' ? 'selected' : '' }}>Business - Corsi - Formazione</option>
                                            <option value="comunita" {{ event.category == 'comunita' ? 'selected' : '' }}>Comunità - Feste - Sagre</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Stato Evento:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select-search-standard form-select" data-search-enabled="true" id="status" name="status" value="{{ event.status }}">
                                            <option value="EventScheduled" {{ event.status == 'EventScheduled' ? 'selected' : '' }}>In programma</option>
                                            <option value="EventPostponed" {{ event.status == 'EventPostponed' ? 'selected' : '' }}>Rinviato</option>
                                            <option value="EventCancelled" {{ event.status == 'EventCancelled' ? 'selected' : '' }}>Cancellato</option>
                                            <option value="EventMovedOnline" {{ event.status == 'EventMovedOnline' ? 'selected' : '' }}>Spostato online</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Titolo:</label>
                                <div class="col-lg-9">
                                    <input role="presentation" autocomplete="off" type="text" name="name" class="form-control maxlength" maxlength="100" placeholder="Titolo dell'evento" value="{{ event.name }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Descrizione:</label>
                                <div class="col-lg-9">
                                    <textarea cols="18" rows="5" name="description" class="form-control summernote" placeholder="Inserisci una descrizione...">{{ event.description }}</textarea>
                                </div>
                            </div>
                            <legend class="text-bold">
                                <i class="icon-location4"></i>
                                Luogo
                                <a class="control-arrow collapsed" data-toggle="collapse" data-target="#event-place" aria-expanded="true">
                                    <i class="icon-circle-down2"></i>
                                </a>
                            </legend>
                            <!--
                            @LEO
                             SE TIPO = OFFLINE MOSTRO SOLO INDIRIZZI
                            SE TIPO = ONLINE MOSTRO SOLO LINK INDIRIZZO VIRTUALE
                            SE TIPO = MIXED MOSTRO ENTRAMBI
                            -->
                            <div class="collapse in" id="event-place" aria-expanded="true">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Indirizzo completo:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="fullAddress" id="fullAddress" class="form-control maxlength" maxlength="250" placeholder="Indirizzo" value="{{ event.fullAddress }}">
                                        <span class="help-block no-margin-bottom">Autocompleta i campi sottostanti</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Stato:</label>
                                    <div class="col-lg-9">
                                        <select class="form-control select-search-standard" name="countryCode" id="countryCode">
                                            <option value="">-</option>
                                            {% for item in lookup("country") %}
                                            <option value="{{ item.code }}" {{ item.code == event.countryCode ? 'selected' : ''}}>{{ item.description }}</option>
                                            {% endfor %}
                                        </select>                                    
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Indirizzo:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="address" id="address" class="form-control maxlength" maxlength="100" placeholder="Indirizzo" value="{{ event.address }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Città:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="city" id="city" class="form-control maxlength" maxlength="100" placeholder="Città" value="{{ event.city }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">CAP:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="10" placeholder="CAP" value="{{ event.postalCode }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Provincia:</label>
                                    <div class="col-lg-9">
                                        <select class="form-control select-search-standard" name="provinceCode" id="provinceCode">
                                            <option value="">-</option>
                                            {% for item in lookup("province") %}
                                            <option value="{{ item.code }}" {{ item.code == event.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Dettagli Indirizzo:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="extraAddress" id="extraAddress" class="form-control maxlength" maxlength="100" placeholder="Dettagli Indirizzo" value="{{ event.extraAddress }}">
                                    </div>
                                </div>

                                <!-- Date -->
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Data inizio:</label>
                                    <div class="col-lg-9">
                                        <input name="startDate" id="startDate" type="text" class="form-control daterange-single" placeholder="Seleziona una data" value="{{ event.startDate | date('dd/MM/yyyy') }}" required>
                                    </div>
                                </div>
                                <!-- Time -->
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Ora inizio:</label>
                                    <div class="col-lg-9">
                                        <input type="text"  name="startHour" class="form-control flatpickr" data-enableTime="true" data-noCalendar="true" placeholder="Ora inizio" value="{{ event.startHour }}" required>
                                    </div>
                                </div>
                                <!-- Date -->
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Data fine:</label>
                                    <div class="col-lg-9">
                                        <input name="endDate" id="endDate" type="text" class="form-control daterange-single" placeholder="Seleziona una data" value="{{ event.endDate | date('dd/MM/yyyy') }}" required>
                                    </div>
                                </div>
                                <!-- Time -->
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Ora fine:</label>
                                    <div class="col-lg-9">
                                        <input type="text"  name="endHour" class="form-control flatpickr" data-enableTime="true" data-noCalendar="true" placeholder="Ora fine" value="{{ event.endHour }}">
                                    </div>
                                </div>
                                <!-- Ticket url -->
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Url (Info / Biglietti):</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" id="ticketsUrl" name="ticketsUrl" placeholder="Url (Info / Biglietti)" value="{{ event.ticketsUrl }}">
                                    </div>
                                </div>
                                <!-- Price -->
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Prezzo biglietto:</label>
                                    <div class="col-lg-9">
                                        <input type="number" min="0" max="999999" class="form-control apply-changes" name="ticketsMinPrice" id="ticketsMinPrice" placeholder="Prezzo biglietto" aria-label="Prezzo biglietto" value="{{ event.ticketsMinPrice | numberformat("#0") }}">
                                    </div>
                                </div>
                                <!-- Free -->
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Segna come evento gratuito</label>
                                    <div class="col-lg-9">
                                        <input class="form-check-input" type="checkbox" role="switch"  id="freeEntry" name="freeEntry"  {{ event.freeEntry == true ? 'checked' : '' }}>
                                    </div>
                                </div>
                                <div id="pageInitials" style="display: none;">[{% for pageId in event.pageIds %}{% set pageRelated = get('page', pageId) %}{"id": "{{ pageId }}", "text": "{{ pageRelated.name }}", "isFake": "{{ pageRelated.isFake }}"}{{ loop.last ? '' : ',' }}{% endfor %}{% if event.pageIds is not empty and event.fakePageIds is not empty %}{% endif %}]</div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Pagine coinvolte</label>
                                    <div class="col-lg-9">
                                        <select id="pageIds" class="form-control" name="pageIds" multiple="multiple" data-tags="true" required>
                                            <!-- ...altri tag... -->
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-12 d-none">
                                    <!-- Container per i tag selezionati -->
                                    <div id="selected-pages-container" style="display: none;"></div>  
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Numero Followers:</label>
                                    <div class="col-lg-9">
                                        <input type="number" min="0" name="followers" id="followers" class="form-control maxlength" maxlength="10" value="{{ event.followers }}">
                                    </div>
                                </div>
                                <!-- Show Followers -->
                                <div class="col-md-4">
                                    <label class="form-label">Mostra numero follower</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" role="switch" id="showFollowers" name="showFollowers" {{ event.showFollowers == true ? 'checked' : '' }}>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements visible-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="pull-right">
                                    <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                    {% if event.id is not empty %}
                                    <button id="btn-delete" type="button" class="btn heading-btn btn-danger">Elimina</button>
                                    {% endif %}
                                    <button id="btn-save" type="submit" class="btn heading-btn btn-primary">Salva<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /event content -->

{% endblock %}
