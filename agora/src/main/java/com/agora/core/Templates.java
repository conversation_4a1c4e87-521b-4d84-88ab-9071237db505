package com.agora.core;

/**
 *
 * <AUTHOR>
 */
public class Templates {

    public static final String EMPTY                               = "empty.html";

    ///////////
    // FRONTEND
    ///////////

    // not found
    public static final String ERROR_404                           = "404.html";

    // landing
    public static final String LANDING_PAGE                        = "fe/landing-page.html";

    // home
    public static final String HOME                                = "fe/home.html";
    public static final String WALL                                = "fe/wall.html";
    public static final String MEDIAKIT                            = "fe/mediakit.html";
    public static final String EVENTS                              = "fe/events.html";
    public static final String ABOUT                               = "fe/about.html";
    public static final String TOS                                 = "fe/tos.html";
    public static final String PAGE_DETAIL                         = "fe/page-detail.html";
    public static final String PAGE_DIFF                           = "fe/page-diff.html";
    public static final String PAGE_CLAIM                          = "fe/page-claim.html";
    public static final String PAGE_ADD                            = "fe/page-add.html";
    public static final String PAGE_EDIT                           = "fe/page-edit.html";
    public static final String PAGE_IFRAME                         = "fe/page-iframe.html";
    public static final String PAGE_SEARCH_IFRAME                  = "fe/page-search-iframe.html";
    public static final String EVENT_DETAIL                        = "fe/event-detail.html";
    public static final String EVENT_ADD                           = "fe/event-add.html";
    public static final String EVENT_EDIT                          = "fe/event-edit.html";
    public static final String EVENT_CLONE                         = "fe/event-clone.html";
    public static final String SITEMAP                             = "fe/sitemap.html";
    public static final String NOTIFICATION_DETAIL                 = "fe/notification-detail.html";
    public static final String NOTIFICATION_ADD                    = "fe/notification-add.html";
    public static final String NOTIFICATION_EDIT                   = "fe/notification-edit.html";

    public static final String RESULTS                             = "fe/results.html";

    // da eliminare!!!
    public static final String CONTACTS                            = "fe/contacts.html";

    // login
    public static final String ACCESS                              = "fe/access.html";
    public static final String REGISTER                            = "fe/register.html";
    public static final String RECOVER                             = "fe/recover.html";
    public static final String REGISTER_BUSINESS                   = "fe/register-business.html";

    // account
    public static final String ACCOUNT                             = "fe/account.html";
    public static final String ACCOUNT_DELETED                     = "fe/account-deleted.html";
    public static final String ACCOUNT_INFO                        = "fe/account-info.html";
    public static final String ACCOUNT_PAGES                        = "fe/account-pages.html";
    public static final String ACCOUNT_CALENDAR                    = "fe/account-calendar.html";

    public static final String ACCOUNT_NOTIFICATIONS               = "fe/account-notifications.html";
    public static final String ACCOUNT_ANNOUNCEMENTS               = "fe/account-announcements.html";
    public static final String ACCOUNT_CONFIRM                     = "fe/account-confirm.html";

    //////////
    // BACKEND
    //////////

    // dashboard
    public static final String DASHBOARD                           = "be/dashboard.html";

    // profile
    public static final String PROFILE                             = "be/profile.html";

    // firm
    public static final String FIRM                                = "be/firm.html";

    // smtp
    public static final String SMTP                                = "be/smtp.html";

    // login
    public static final String LOGIN                               = "be/login.html";
    public static final String FORGOT                              = "be/forgot.html";

    // announcements
    public static final String ANNOUNCEMENTS                       = "be/announcements.html";
    public static final String ANNOUNCEMENT_VIEW                   = "be/announcement-view.html";

    // customers
    public static final String CUSTOMERS                           = "be/customers.html";
    public static final String CUSTOMERS_ADD                       = "be/customers-add.html";
    public static final String CUSTOMER_VIEW                       = "be/customer-view.html";

    // areas
    public static final String AREAS                               = "be/areas.html";
    public static final String AREA_EDIT                           = "be/area-edit.html";

    // categories
    public static final String CATEGORIES                          = "be/categories.html";
    public static final String CATEGORY_EDIT                       = "be/category-edit.html";

    // subcategories
    public static final String SUBCATEGORIES                       = "be/subcategories.html";
    public static final String SUBCATEGORY_EDIT                    = "be/subcategory-edit.html";

    // vendors
    public static final String VENDORS                             = "be/vendors.html";
    public static final String VENDORS_ADD                         = "be/vendors-add.html";
    public static final String VENDOR_VIEW                         = "be/vendor-view.html";

    // labels
    public static final String LABELS                              = "be/labels.html";

    // post
    public static final String POSTS                               = "be/posts.html";
    public static final String POST_EDIT                           = "be/post-edit.html";

    // searches
    public static final String BE_SEARCHES                         = "be/searches.html";

    // eventrequests
    public static final String BE_EVENT_REQUESTS                   = "be/eventrequests.html";

    // pagereport
    public static final String BE_PAGE_REPORTS                    = "be/pagereports.html";

    // pageclaim
    public static final String BE_PAGE_CLAIMS                     = "be/pageclaims.html";

    // eventreport
    public static final String BE_EVENT_REPORTS                   = "be/eventreports.html";


    // events
    public static final String BE_EVENTS                           = "be/events.html";
    public static final String BE_EVENT_EDIT                       = "be/event-edit.html";

    // pages
    public static final String BE_PAGES                            = "be/pages.html";
    public static final String BE_PAGE_EDIT                        = "be/page-edit.html";

    // upload
    public static final String PAGES_UPLOAD                        = "be/pages-upload.html";
    public static final String PAGES_UPLOAD_FOTO                   = "be/pages-upload-foto.html";
    public static final String EVENTS_UPLOAD                       = "be/events-upload.html";

    // zucchetti
    public static final String MAILNOTIFICATIONS                   = "be/mailnotifications.html";
    public static final String MAILNOTIFICATION_VIEW               = "be/mailnotification-view.html";


    // home slider
    public static final String HOME_SLIDERS                        = "be/home-slider.html";
    public static final String HOME_SLIDER_EDIT                    = "be/home-slider-edit.html";

    // sponsor events
    public static final String SPONSOR_EVENT_COLLECTION            = "be/sponsor-event-collection.html";
    public static final String SPONSOR_EVENT_EDIT                  = "be/sponsor-event-edit.html";

    // sponsor pages
    public static final String SPONSOR_PAGE_COLLECTION             = "be/sponsor-page-collection.html";
    public static final String SPONSOR_PAGE_EDIT                   = "be/sponsor-page-edit.html";

    // analysis
    public static final String ANALYSIS_DATA                       = "be/analysis-data.html";
    public static final String ANALYSIS_PIVOT                      = "be/analysis-pivot.html";


    //////////
    // SUPPORT
    //////////

    // 404
    public static final String PAGE_404                            = "fe/404.html";

}
