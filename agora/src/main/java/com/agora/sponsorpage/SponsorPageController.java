package com.agora.sponsorpage;

import com.agora.commons.SponsorPageCommons;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.SponsorPageDao;
import com.agora.pojo.SponsorPage;
import com.agora.pojo.User;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for SponsorPage functionality
 * 
 * <AUTHOR> Agent
 */
public class SponsorPageController {

    public static TemplateViewRoute sponsor_page_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }

        // check permissions
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), "system")
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), "admin")) {
            response.redirect(RouteUtils.contextPath(request) + Paths.DASHBOARD);
            return Manager.renderEmpty();
        }

        attributes.put("user", user);

        List<SponsorPageCommons.SponsorPageEntry> sponsorPageEntries = SponsorPageCommons.loadSponsorPageEntries();
        attributes.put("sponsorPageEntries", sponsorPageEntries);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.SPONSOR_PAGE_COLLECTION, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute sponsor_page_edit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }

        // check permissions
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), "system")
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), "admin")) {
            response.redirect(RouteUtils.contextPath(request) + Paths.DASHBOARD);
            return Manager.renderEmpty();
        }

        attributes.put("user", user);

        ObjectId sponsorPageId = ParamUtils.toObjectId(request.queryParams("sponsorPageId"));
        SponsorPage sponsorPage;
        if (sponsorPageId != null) {
            sponsorPage = SponsorPageDao.loadSponsorPage(sponsorPageId);
        } else {
            sponsorPage = new SponsorPage();
        }
        attributes.put("sponsorPage", sponsorPage);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.SPONSOR_PAGE_EDIT, attributes, RouteUtils.pathType(request));
    };

    public static Route sponsor_page_edit_save = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }

        // check permissions
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), "system")
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), "admin")) {
            response.redirect(RouteUtils.contextPath(request) + Paths.DASHBOARD);
            return null;
        }

        // params
        Map<String, String> params = PojoUtils.paramsFromRequest(request);
        ObjectId sponsorPageId = null;
        if (request.queryParams("sponsorPageId") != null) {
            sponsorPageId = ParamUtils.toObjectId(request.queryParams("sponsorPageId"));
        }

        SponsorPage sponsorPage;
        if (sponsorPageId != null) {
            sponsorPage = SponsorPageDao.loadSponsorPage(sponsorPageId);
        } else {
            sponsorPage = new SponsorPage();
        }

        // merge params
        sponsorPage = PojoUtils.mergeFromParams(params, sponsorPage);

        try {
            if (sponsorPageId != null) {
                SponsorPageDao.updateSponsorPage(sponsorPage);
            } else {
                SponsorPageDao.insertSponsorPage(sponsorPage);
            }
            response.redirect(RouteUtils.contextPath(request) + Paths.SPONSOR_PAGE_COLLECTION + "/ok");
        } catch (Exception ex) {
            response.redirect(RouteUtils.contextPath(request) + Paths.SPONSOR_PAGE_EDIT + "/error");
        }

        return null;
    };

    public static Route sponsor_page_remove = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }

        // check permissions
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), "system")
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), "admin")) {
            response.redirect(RouteUtils.contextPath(request) + Paths.DASHBOARD);
            return null;
        }

        ObjectId sponsorPageId = ParamUtils.toObjectId(request.queryParams("sponsorPageId"));
        if (sponsorPageId != null) {
            try {
                SponsorPageDao.removeSponsorPage(sponsorPageId);
                return "ok";
            } catch (Exception ex) {
                response.status(500);
                return "error";
            }
        }

        response.status(400);
        return "error";
    };
}
